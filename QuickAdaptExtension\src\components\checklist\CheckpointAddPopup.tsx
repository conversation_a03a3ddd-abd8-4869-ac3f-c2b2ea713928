import React, { useReducer, useState,useEffect, useRef, useContext, useCallback } from "react";
import { Box, Typography, TextField, Grid, IconButton, Button, InputAdornment, FormControl, InputLabel, Select, MenuItem, SelectChangeEvent, FormControlLabel, Switch, Tooltip, CircularProgress, ClickAwayListener } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { useTranslation } from 'react-i18next';
import useDrawerStore, { BUTTON_CONT_DEF_VALUE_1, CANVAS_DEFAULT_VALUE, IMG_CONT_DEF_VALUE } from "../../store/drawerStore";
import { HOTSPOT_DEFAULT_VALUE } from "../../store/drawerStore";
import SearchIcon from "@mui/icons-material/Search";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import InsertPhotoIcon from "@mui/icons-material/InsertPhoto";
import PersonIcon from "@mui/icons-material/Person";
import FavoriteIcon from "@mui/icons-material/Favorite";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import {
  InfoFilled,
  QuestionFill,
  Reselect,
    Solid,
    editicon,
    deletestep,
	chkicn1,
	chkicn2,
	chkicn3,
	chkicn4,
	chkicn5,
	chkicn6,
	redirect,
	warning,
} from "../../assets/icons/icons";
import ArrowBackIosNewOutlinedIcon from "@mui/icons-material/ArrowBackIosNewOutlined";
import CloudUploadOutlinedIcon from '@mui/icons-material/CloudUploadOutlined';
import { getAllGuides } from "../../services/GuideListServices";
import { AccountContext } from "../login/AccountContext";
import AddIcon from "@mui/icons-material/Add";
import '../../styles/rtl_styles.scss';
interface CheckPointAddPopupProps {
    checklistCheckpointProperties: any;
	setChecklistCheckpointProperties:any 
    onClose: () => void;
  
}

const CheckPointAddPopup = ({ checklistCheckpointProperties, setChecklistCheckpointProperties ,onClose}: CheckPointAddPopupProps) => {
	const { t: translate } = useTranslation();
	const [duplicateError, setDuplicateError] = useState<string | null>(null);
	const {
		checklistGuideMetaData,
        setCheckPointsEditPopup,
        checkpointsEditPopup,
        titlePopup,
        setTitlePopup,
        setDesignPopup,
        titleColor,
        setTitleColor,
        checkpointsPopup,
        setCheckPointsPopup,
        checkpointTitleColor,
        setCheckpointTitleColor,
        checkpointTitleDescription,
        setCheckpointTitleDescription,
        checkpointIconColor,
        setCheckpointIconColor,
        setUnlockCheckPointInOrder,
	    unlockCheckPointInOrder,
        checkPointMessage,
        setCheckPointMessage,
		setCheckPointsAddPopup,
		updateChecklistCheckPoints,
		updateChecklistCheckPointItem,
		setIsUnSavedChanges,
		isUnSavedChanges
	
      
	
    } = useDrawerStore((state: any) => state);
    

	const [selectedInteraction, setSelectedInteraction] = useState<string>('');
	const [searchTerm, setSearchTerm] = useState('');

	const [checklistCheckpointListProperties, setChecklistCheckpointListProperties] = useState<any>({
		id:'',
		interaction: '',
		title: '',
		description: '',
		redirectURL: '',
		icon: '',
		supportingMedia: '',
		mediaTitle: '',
		mediaDescription: '',
	});
	

    const handleCheckPointIconColorChange = (e: any) => setCheckpointIconColor(e.target.value);
    const handleCheckPointTitleColorChange = (e: any) => setCheckpointTitleColor(e.target.value);
	const handleCheckPointDescriptionColorChange = (e: any) => setCheckpointTitleColor(e.target.value);
	const [interactions, setInteractions] = useState<any[]>([]);

	useEffect(() => {
		if (selectedInteraction) {
			const selectedInteractionData = filteredInteractions.find(interaction => interaction.Name === selectedInteraction);
			
			if (selectedInteractionData) {
				setChecklistCheckpointListProperties({
					interaction: selectedInteraction,
					title: selectedInteractionData.Name || '',
					description: selectedInteractionData.Description || '',
					redirectURL: selectedInteractionData.TargetUrl || '',
					icon: selectedInteractionData.targetUrl,
					supportingMedia: '',
					mediaTitle: selectedInteractionData.Name || '',
					mediaDescription: selectedInteractionData.description || '',
					id:selectedInteractionData.GuideId||'',
				});
			}
		}
	}, [selectedInteraction, interactions, searchTerm]);
	
		const handleClose = () => {
			setCheckPointsAddPopup(false);
		};
		const handledesignclose = () => {
			setDesignPopup(false);
		};
		const handleSizeChange = (value: number) => {
			const sizeInPx = 16 + (value - 1) * 4;
			onPropertyChange("Size", sizeInPx);
		};

		const onReselectElement = () => {
			
		};

		const onPropertyChange = (key: any, value: any) => {
			setChecklistCheckpointListProperties((prevState: any) => ({
				...prevState,
				[key]: value,
			}));
	};
	const [applyclicked, setapplyClisked] = useState(false);
	const handleApplyChanges = () => {
		setFileError(null);
		setDuplicateError(null);
			checklistCheckpointListProperties.icon = icon;

		// Check if interaction is selected
		if (!checklistCheckpointListProperties.interaction || checklistCheckpointListProperties.interaction.trim() === "") {
			setDuplicateError(translate("Please select an interaction"));
			return;
		}

		// Check for duplicate interaction
		const isDuplicate = checklistCheckpointProperties.checkpointsList?.some(
			(cp: any) => cp.interaction === checklistCheckpointListProperties.interaction
		);
		if (isDuplicate) {
			setDuplicateError(translate("Interaction already used"));
			return;
		}

			const updatedCheckpoint = {
				...checklistCheckpointListProperties,
			};
			updateChecklistCheckPointItem(updatedCheckpoint);
			setapplyClisked(true);
			setIsUnSavedChanges(true);

			setChecklistCheckpointProperties((prev: any) => ({
				...prev,
				checkpointsList: [
					...prev.checkpointsList, // Keep existing checkpoints
					updatedCheckpoint,       // Add the new one at the end
				],
			}));

			handleClose();
		};
		

	const handleEditClick = () => {
        setCheckPointsEditPopup(true);
    }
	const [skip, setSkip] = useState(0);
const [loading, setLoading] = useState(false);
const [hasMore, setHasMore] = useState(true);
const [filteredInteractions, setFilteredInteractions] = useState<any[]>([]);
	const { accountId } = useContext(AccountContext);
	const [isSearching, setIsSearching] = useState(false);
const top = 20;

useEffect(() => {
  fetchData(0);  // Fetch initial data to enable scrolling
}, []);



const fetchData = async (newSkip: number,newsearchTerm:string="") => {
	if (newsearchTerm=="" && loading) return;  // Prevent duplicate calls
	if(newsearchTerm == "" && searchTerm != "") newsearchTerm = searchTerm;
	setLoading(true);
  
	const filters = [
	  {
		FieldName: "AccountId",
		ElementType: "string",
		Condition: "contains",
		Value: accountId,
		IsCustomField: false,
	  },
	  {
		FieldName: "GuideType",
		ElementType: "string",
		Condition: "not equal",
		Value: "Checklist",
		IsCustomField: false,
	  }
	];
	if(newsearchTerm != ""){
		filters.push({
			FieldName:"Name",
			ElementType:"string",
			Condition:"contains",
			Value:newsearchTerm,
			IsCustomField:false
		});
	}
  
	try {
	  const data = await getAllGuides(newSkip, top, filters, "");
	 const newInteractions = data?.results;
   if(newsearchTerm=="") {
	  if (newSkip === 0) {
		setInteractions(newInteractions);  
		setFilteredInteractions(newInteractions);
	  } else {
		setInteractions((prev) => [...prev, ...newInteractions]); 
		setFilteredInteractions((prev) => [...prev, ...newInteractions]);
	  }
	}
	else {
       if(newSkip === 0) setFilteredInteractions(newInteractions);
	   else setFilteredInteractions((prev) => [...prev, ...newInteractions]);
	}
	  setSkip(newSkip + top);
	  setHasMore(newInteractions.length > 0);
	} catch (error) {
	  console.error("Error fetching guides:", error);
	} finally {
	  setLoading(false);
	}
  };
  
const handleMenuScroll = (event: React.UIEvent<HTMLDivElement>) => {
	const target = event.currentTarget;
	const { scrollTop, scrollHeight, clientHeight } = target;
  
	if (scrollHeight - scrollTop - clientHeight < 50 && !loading && hasMore ) {
	   fetchData(skip); // no need to pass searchTerm
	}
  };
  
  const [isInteractionDropdownOpen, setIsInteractionDropdownOpen] = useState(false);
  const handleInteractionChange = (newValue: string) => {
	  setSelectedInteraction(newValue);
	  onPropertyChange("interaction", newValue);
	  setIsInteractionDropdownOpen(false);
	  setSearchTerm('');
		setDuplicateError(null); // Clear duplicate error when selecting a new interaction
  };

  const handleInteractionDropdownOpen = () => {
	  setIsInteractionDropdownOpen(true);
	  if (searchTerm.trim()) {
		fetchData(0, searchTerm); // Or your logic to update `filteredInteractions`
	  } else {
		fetchData(0, ""); // Optional: load default or all interactions if search is empty
	  }
  };

  const handleInteractionDropdownClose = () => {
	  setIsInteractionDropdownOpen(false);
	  setSearchTerm('');
  };
const handleSearch =  (event: React.ChangeEvent<HTMLInputElement>) => {
	const term = event.target.value;
	setSearchTerm(term);
  
	if (!term.trim()) {
	  // If search is cleared
	  setFilteredInteractions(interactions);
	  setIsSearching(false);
	} else {
	//   const filtered = interactions.filter((interaction) =>
	// 	interaction.Name.toLowerCase().includes(term.toLowerCase())
	//   );
	//   setFilteredInteractions(filtered);
	setIsSearching(true);
	setFilteredInteractions([]);
	 fetchData(0,term);
	}
  };
  
	  
  const [icons, setIcons] = useState<any[]>([
	{ id: 1, component: <span dangerouslySetInnerHTML={{ __html: chkicn1 }} style={{ zoom: 1 ,display:"flex"}} />, selected: true },
	{ id: 2, component:  <span dangerouslySetInnerHTML={{ __html: chkicn2 }} style={{ zoom: 1 ,display:"flex"}} />, selected: false },
	{ id: 3, component:  <span dangerouslySetInnerHTML={{ __html: chkicn3 }} style={{ zoom: 1,display:"flex" }} />, selected: false },
	  { id: 4, component:  <span dangerouslySetInnerHTML={{ __html: chkicn4 }} style={{ zoom: 1,display:"flex" }} /> , selected: false },
	  { id: 5, component:  <span dangerouslySetInnerHTML={{ __html: chkicn5 }} style={{ zoom: 1 ,display:"flex"}} />, selected: false },
	  { id: 6, component:  <span dangerouslySetInnerHTML={{ __html: chkicn6 }} style={{ zoom: 1 ,display:"flex"}} />, selected: false },
	]);
const [error, setError] = useState<string | null>(null);

const handleIconClick = async (id: number) => {
    setIcons(prevIcons =>
        prevIcons.map(icon => ({
            ...icon,
            selected: icon.id === id,
        }))
    );

    const selectedIcon = icons.find(icon => icon.id === id);
    if (selectedIcon) {
        const svgElement = selectedIcon.component.props.dangerouslySetInnerHTML?.__html;
        if (svgElement) {
            const base64Icon = svgToBase64(svgElement);
            setIcon(base64Icon);
            checklistCheckpointListProperties.icon=base64Icon
        }
    }
};

// Helper function to convert SVG to Base64
const svgToBase64 = (svgString: string): string => {
    return `data:image/svg+xml;base64,${btoa(svgString)}`;
};


const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
	const file = event.target.files?.[0];
	if (!file) return;

	const isIco = file.name.endsWith(".ico");

	// Validate the file type and size
	const img = new Image();
	img.src = URL.createObjectURL(file);
	img.onload = () => {
		if (!isIco || img.width > 64 || img.height > 64) {
			setError(translate("Please upload an .ico file less than 64x64px"));
		} else {
			setError(null);
			setIcons(prevIcons => [
				...prevIcons,
				{ id: prevIcons.length + 1, component: <img src={img.src} alt="Custom Icon" width={24} />, selected: false },
			]);
		}
	};
	};
	

	
	const [icon, setIcon] = useState<any>();

useEffect(() => {
  const initialSelectedIcon = icons.find(icon => icon.selected);
  if (initialSelectedIcon && !checklistCheckpointListProperties.icon) {
        const svgElement = initialSelectedIcon.component.props.dangerouslySetInnerHTML?.__html;
        if (svgElement) {
            const base64Icon = svgToBase64(svgElement);
            setIcon(base64Icon);
			checklistCheckpointListProperties.icon=base64Icon

    }
  }
}, []); 

	const [files, setFiles] = useState<File[]>([]);
	const [gifFile, setGifFile] = useState<File | null>(null);
	const [videoFile, setVideoFile] = useState<File | null>(null);
	const [imageType, setImageType] = useState<string | null>(null); // Track locked image type
	const [fileError, setFileError] = useState<string | null>(null);

	const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
		setFileError(null);
		if (!event.target.files) return;
	
		const newFiles = Array.from(event.target.files);
	
		const isAllGif = newFiles.every(file => file.type === "image/gif");
		const isAllMp4 = newFiles.every(file => file.type === "video/mp4");
		const isAllImages = newFiles.every(file =>
			["image/jpeg", "image/png", "image/jpg"].includes(file.type)
		);
		const isMixedType = !(isAllGif || isAllMp4 || isAllImages);
	
		if (isMixedType) {
			setFileError(translate("Mixed file formats are not allowed."));
			return;
		}
	
		if (gifFile) {
			if (isAllGif) {
				setFileError(translate("Only one GIF is allowed."));
				return;
			} else {
				setFileError(translate("Mixed file formats are not allowed."));
				return;
			}
		}
	
		if (videoFile) {
			if (isAllMp4) {
				setFileError(translate("Only one Video is allowed."));
				return;
			} else {
				setFileError(translate("Mixed file formats are not allowed."));
				return;
			}
		}
	
		if (files.length > 0) {
			if (!isAllImages) {
				setFileError(translate("Mixed file formats are not allowed."));
				return;
			}
			if (imageType && !newFiles.every(file => file.type === imageType)) {
				setFileError(translate("Mixed file formats are not allowed."));
				return;
			}
		}
	
		if (isAllGif) {
			if (newFiles.length > 1) {
				setFileError(translate("Only one GIF is allowed."));
				return;
			}
			setGifFile(newFiles[0]);
		} else if (isAllMp4) {
			if (newFiles.length > 1) {
				setFileError(translate("Only one Video is allowed."));
				return;
			}
			setVideoFile(newFiles[0]);
		} else if (isAllImages) {
			const newImageType = newFiles[0].type;
			if (!imageType) {
				setImageType(newImageType); // Lock the image type
			}
			setFiles(prevFiles => {
				const updatedFiles = [...prevFiles, ...newFiles];
				updatedFiles.sort((a, b) => {
					const nameA = a.name.match(/\d+/) ? parseInt(a.name.match(/\d+/)![0], 10) : 0;
					const nameB = b.name.match(/\d+/) ? parseInt(b.name.match(/\d+/)![0], 10) : 0;
					return nameA - nameB;
				});
				return updatedFiles;
			});
		}
	
		const base64Files = await Promise.all(
			newFiles.map(async (file) => ({
				Name: file.name,
				Type: file.type,
				Base64: await fileToBase64(file),
			}))
		);
	
		setChecklistCheckpointListProperties((prevState: any) => {
			const updatedMedia = [...(prevState.supportingMedia || []), ...base64Files];
			updatedMedia.sort((a, b) => {
				const nameA = a.Name.match(/\d+/) ? parseInt(a.Name.match(/\d+/)![0], 10) : 0;
				const nameB = b.Name.match(/\d+/) ? parseInt(b.Name.match(/\d+/)![0], 10) : 0;
				return nameA - nameB;
			});
			return {
				...prevState,
				supportingMedia: updatedMedia,
			};
		});
	};
	
	
	  
	const fileToBase64 = (file: File): Promise<string> => {
		return new Promise((resolve, reject) => {
			const reader = new FileReader();
			reader.readAsDataURL(file);
			reader.onload = () => resolve(reader.result as string);
			reader.onerror = error => reject(error);
		});
	};
	
	
	
	const handleDeleteFile = (index: number) => {
		setFileError(null);
		setFiles((prevFiles) => {
			const newFiles = prevFiles.filter((_, i) => i !== index);
			if (newFiles.length === 0) setImageType(null); // Reset image lock
	
			setChecklistCheckpointListProperties((prevProperties: any) => ({
				...prevProperties,
				supportingMedia: prevProperties.supportingMedia.filter((_: any, i: any) => i !== index),
			}));
	
			return newFiles;
		});
	};
	
	const handleDeleteGif = () => {
		setGifFile(null);
		setChecklistCheckpointListProperties((prevProperties: any) => ({
			...prevProperties,
			supportingMedia: prevProperties.supportingMedia.filter(
				(file: any) => !file.Name?.toLowerCase().endsWith(".gif")
			),
		}));
	};
	
	const handleDeleteVideo = () => {
		setVideoFile(null);
		setChecklistCheckpointListProperties((prevProperties: any) => ({
			...prevProperties,
			supportingMedia: prevProperties.supportingMedia.filter(
				(file: any) => !file.Name?.toLowerCase().endsWith(".mp4")
			),
		}));
	};
	
	
				  
	  
	
	return (
			
		<>
			<div
				id="qadpt-designpopup"
				className="qadpt-designpopup"
			>
				<div className="qadpt-content">
					<div className="qadpt-design-header">
						<IconButton
							aria-label="back"
							onClick={handleClose}
						>
							<ArrowBackIosNewOutlinedIcon />
						</IconButton>
						<div className="qadpt-title">{translate("Step")} {checklistGuideMetaData[0].checkpoints.checkpointsList.length + 1}</div>
						<IconButton
							size="small"
							aria-label="close"
							onClick={onClose}
						>
							<CloseIcon />
						</IconButton>
					</div>


					
					<div className="qadpt-canblock">
						<div className="qadpt-controls">
						<Box
    id="qadpt-designpopup"
    className="qadpt-control-box qadpt-chkcontrol-box"
    // sx={{ flexDirection: "column", height: "auto !important", padding: "0px !important" }}
  >
    <Typography className="qadpt-control-label" sx={{ padding: "0 0 8px 0 !important" }}>
									{translate("Interaction")}
    </Typography>

    <FormControl
      variant="outlined"
      fullWidth
      className="qadpt-control-input"
      sx={{
        width: "100% !important",
        borderRadius: "12px",
        padding: "0",
        margin: "0 !important",
      }}
    >
									<Select
										open={isInteractionDropdownOpen}
										onOpen={handleInteractionDropdownOpen}
										onClose={handleInteractionDropdownClose}
        value={selectedInteraction}
        onChange={(e) => {
          const newValue = e.target.value as string;
          setSelectedInteraction(newValue);
          onPropertyChange("interaction", newValue);
										}}
										
        name="ShowUpon"
        displayEmpty
										sx={{
											width: "100% !important",
											textAlign: "left", 
											"& .MuiOutlinedInput-root": {
												"&:hover": {
													borderColor: "none !important",
												},
												"&.Mui-focused": {
													borderColor: "none !important",
												},
											},
											"& .MuiOutlinedInput-notchedOutline": {
												border : "none !important"
											},
											  "&.MuiInputBase-root":{height:"35px !important"}
										}}
										
        MenuProps={{
          PaperProps: {
            sx: {
              maxHeight: 430,
				  overflowY: "auto",
				  maxWidth: "220px",
				  "& ul li": {
					whiteSpace: "normal",
					wordBreak: "break-word", 
					overflowWrap: "break-word", 
					maxWidth: "100%",
				  },
				  "& .MuiFormControl-root .MuiInputBase-root": {
					  borderRadius: "12px",
					  padding:"1px !important"
				  },
				  "& .MuiOutlinedInput-root": {
					"&:hover": {
					  borderColor: "#ccc !important", 
					},
					"&.Mui-focused": {
					  borderColor: "#ccc !important", 
					},
				  },
				  "& .MuiOutlinedInput-notchedOutline": {
					  borderColor: "#ccc !important",
					borderWidth:"1px !important"  
				  },
            },
			},
			onClose: handleInteractionDropdownClose,
										}}
										
										renderValue={(selected) => selected || translate("Select Interaction")}
      >

        <Box
          sx={{
            position: "sticky",
            top: 0,
            left: 0,
            right: 0,
            zIndex: 1,
            backgroundColor: "white",
            padding: "8px",
            borderBottom: "1px solid #eee",
          }}
         // onClick={(e) => e.stopPropagation()} // Prevent click from closing dropdown
          onKeyDown={(e) => e.stopPropagation()} // Prevent dropdown from closing on typing
        >
          <TextField
            fullWidth
												placeholder={translate("Search interactions...")}
            variant="outlined"
            size="small"
												value={searchTerm}
												onClick={(e) => {
													e.stopPropagation(); // Prevent TextField click from closing dropdown
													setIsInteractionDropdownOpen(true);
												  }}
												  onKeyDown={(e) => {
													e.stopPropagation(); // Prevent keypress from closing dropdown
													if (e.key === "Escape") {
													  handleInteractionDropdownClose(); // Allow Escape to close dropdown
													}
													if (e.key === "Enter" ) {
														searchTerm.trim()?fetchData(0, searchTerm): fetchData(0, ""); // 🔁 Same logic as icon button
													  }
												  }}
												  onChange={(e) => setSearchTerm(e.target.value)}// Call search handler directly
												autoFocus
												InputProps={{
													startAdornment: (
													  <InputAdornment position="start">
														<IconButton
														  aria-label="search"
														  onClick={(e) => {
															e.stopPropagation();
															if (searchTerm.trim()) {  // Only search if there's a non-empty term
															  fetchData(0, searchTerm);
															}
															else{
																fetchData(0, "");
															}
														  }}
														>
														  <SearchIcon />
														</IconButton>
													  </InputAdornment>
													),
												  }}
												sx={{
													"& .MuiOutlinedInput-root": { borderRadius: "4px" },
												"& .MuiInputAdornment-root": {
													margin:"0 !important"
													}
												}}
          />
        </Box>

        {/* Scrollable Content */}
        <Box
          sx={{
            maxHeight: "352px",
            overflowY: "auto",
          }}
          onScroll={handleMenuScroll} // Attach the event directly here
        >
          {filteredInteractions && filteredInteractions.length === 0 && !loading && (
												<MenuItem disabled>{translate("No interactions found")}</MenuItem>
          )}
          {filteredInteractions && filteredInteractions.map((interaction) => (
            <MenuItem
              key={interaction.GuideId}
              value={interaction.Name}
              onClick={() => {
				handleInteractionChange(interaction.Name);
				 // onPropertyChange("interaction", interaction.Name);
              }}
            >
              {interaction.Name}
            </MenuItem>
          ))}
          {loading && (
            <MenuItem disabled sx={{ display: "flex", justifyContent: "center" }}>
              <CircularProgress size={20} />
            </MenuItem>
          )}
        </Box>
      </Select>
    </FormControl>
								{duplicateError && (
									<div style={{ color: '#e53935', padding: 10, marginBottom: 8, fontSize: 13, textAlign: 'left' }}>
										{duplicateError}
									</div>
								)}
  </Box>
							<Box
									id="qadpt-designpopup"
									className="qadpt-control-box qadpt-chkcontrol-box"
								>
								<Typography className="qadpt-control-label" sx={{ padding: "0 !important", marginBottom: "8px !important" }}>{translate("Title")}</Typography>
		  							
									<TextField
											variant="outlined"
                                            size="small"
									placeholder={translate("Step Title")}
                                            className="qadpt-control-input"
									style={{ width: "100%" }}
									value={checklistCheckpointListProperties.title}
											onChange={(e) => onPropertyChange("title", e.target.value)}
											InputProps={{
												endAdornment: "",
												sx: {
								 
													"&:hover .MuiOutlinedInput-notchedOutline": { border: "none" }, 
													"&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "none" },
													"& fieldset":{border:"none"},
													"& input": { textAlign: "left !important" ,paddingLeft:"10px !important"},
													"&.MuiInputBase-root":{height:"auto !important"}
												},
											}}
										/>
							</Box>
							

							<Box
									id="qadpt-designpopup"
									className="qadpt-control-box qadpt-chkcontrol-box"
								>
								<Typography className="qadpt-control-label" sx={{ padding: "0 !important", marginBottom: "8px !important" }} >{translate("Description")}</Typography>

									<TextField
											variant="outlined"
                                            size="small"
									placeholder={translate("Step Desc")}
									className="qadpt-control-input"
									multiline
									minRows={3}
									value={checklistCheckpointListProperties.description}
									onChange={(e) => onPropertyChange("description", e.target.value)}

                                            style={{width:"100%"}}
											InputProps={{
												endAdornment: "",
												sx: {
								 
													"&:hover .MuiOutlinedInput-notchedOutline": { border: "none" }, 
													"&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "none" },
													"& fieldset":{border:"none"},
													"& input": { textAlign: "left !important" ,paddingLeft:"10px !important"},
													"&.MuiInputBase-root":{height:"auto !important"}
												},
											}}
										/>
							</Box>
							


							<Box
									id="qadpt-designpopup"
									className="qadpt-control-box qadpt-chkcontrol-box"
							>
								<div className="qadpt-control-label" style={{display:"flex",flexDirection:"row",alignItems:"center",gap:"5px",padding:"0" ,marginBottom:"10px"}}>
									<Typography sx={{ color: "#444444", fontWeight: "600" }}>{translate("Redirect URL")}</Typography>
									<span dangerouslySetInnerHTML={{ __html: redirect }} style={{ display: "flex" }} /> </div>
								<Typography style={{ fontSize: "11px", color: "#8d8d8d", textAlign: "left", padding: "0", marginBottom: "10px" }}>									{translate("User will be navigated to redirected URL for triggering the interactions.Helpful for Tooltips")}</Typography>

									<TextField
											variant="outlined"
                                            size="small"
									placeholder={translate("Redirection URL")}
                                            className="qadpt-control-input"
                                            style={{width: "100%"}}
									value={checklistCheckpointListProperties.redirectURL}
											onChange={(e) => onPropertyChange("redirectURL", e.target.value)}
											InputProps={{
												endAdornment: "",
												sx: {
								 
													"&:hover .MuiOutlinedInput-notchedOutline": { border: "none" }, 
													"&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "none" },
													"& fieldset":{border:"none"},
													"& input": { textAlign: "left !important" ,paddingLeft:"10px !important"},
													"&.MuiInputBase-root":{height:"auto !important"}
												},
											}}
										/>
							</Box>
							


							<Box id="qadpt-designpopup" className="qadpt-control-box qadpt-chkcontrol-box"
								// sx={{ flexDirection: "column", height: "auto !important", padding: "0 !important" }}
							>
								<Typography className="qadpt-control-label" sx={{ padding: "0 0 8px 0 !important" }}
								>{translate("Icon")}</Typography>
            <Box sx={{ display: "flex", gap: 1, alignItems: "center" ,width:"-webkit-fill-available",flexWrap:"wrap"}}>
                {icons.map(icon => (
                    <Tooltip key={icon.id} title="Select Icon" arrow>
                        <IconButton
                            onClick={() => handleIconClick(icon.id)}
                            sx={{
                                border: icon.selected ? "2px solid var(--primarycolor)" : "none",
                                borderRadius: "8px",
								padding: "8px",
								background:"#F1ECEC",
                            }}
                        >
                            {icon.component}
                        </IconButton>
                    </Tooltip>
                ))}

            
            </Box>

          
        </Box>
							

		<Box
      id="qadpt-designpopup"
      className="qadpt-control-box qadpt-chkcontrol-box"
    //   sx={{ flexDirection: "column", height: "auto !important",padding:"0 !important"}}
    >
								<Typography className="qadpt-control-label" sx={{ padding: "0 0 8px 0 !important" }}>{translate("Supporting Media")}</Typography>

	  <div
  style={{
    width: "165px",
    height: "auto",
    margin: "0 8px 8px 8px",
    display: "flex",
    flexDirection: "column", 
    alignItems: "center",
    justifyContent: "center",
    border: "1px dashed var(--primarycolor)",
    borderRadius: "12px",
    padding: "8px",
    background: "#F1ECEC",
    textAlign: "center",
  }}
>
  <Button
    className="qadpt-upload-button"
    style={{
		height: "auto",
		padding: "0",
      width: "100%",
      display: "flex",
      flexDirection: "row", // Ensures icon & text are in one line
      alignItems: "center",
      justifyContent: "center",
      gap: "6px",
      color: "#000",
      backgroundColor: "#F1ECEC",
      textTransform: "capitalize",
      boxShadow: "none",
    }}
    component="label"
  >
    <CloudUploadOutlinedIcon sx={{zoom:"1.6"}} />
    Upload file
    <input
      id="file-input"
      type="file"
      multiple
      accept=".jpeg, .jpg, .png, .gif, .mp4"
      onChange={handleFileChange}
      style={{ display: "none" }}
    />
  </Button>
  
  {/* File format text on the second line */}
  <Typography style={{ fontSize: "12px", color: "#A3A3A3" }}>
    .png, .jpg, .gif, .mp4
  </Typography>
</div>
{fileError && (
 	<div style={{ display: "flex", alignItems: "center" , color: "#e6a957",padding:"0 8px",textAlign:"left", width: "-webkit-fill-available"}}>

    <span
      style={{ marginRight: "4px",display:"flex" }}
      dangerouslySetInnerHTML={{ __html: warning }}
    />
    <div style={{fontSize: "12px"}}>{fileError}</div>
  </div>
)}



      {/* Display uploaded images */}
      <Box sx={{width:"-webkit-fill-available"}}>
        {files.map((file, index) => (
          <Box
            key={index}
            display="flex"
            alignItems="center"
            justifyContent="space-between"
			sx={{
				borderRadius: "12px",
				padding: "8px",
				margin: "8px",
				backgroundColor: "#e5dada",
			  }}
			  
          >
            <img
              src={URL.createObjectURL(file)}
              alt={`uploaded-${index}`}
              style={{ width: "20px", height: "20px", borderRadius: "5px" }}
            />
				{/* //<span dangerouslySetInnerHTML={{ __html: imageIcon }} style={{ zoom: 0.7 }} /> */}
            <Typography
              sx={{ flex: 1, fontSize: "14px", wordBreak: "break-word" }}
            >
              {file.name}
            </Typography>
            <IconButton onClick={() => handleDeleteFile(index)} size="small">
			<span dangerouslySetInnerHTML={{ __html: deletestep }} style={{ zoom: "1" ,display:"flex" }} />
            </IconButton>
          </Box>
        ))}

        {/* Display uploaded GIF separately */}
        {gifFile && (
          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            sx={{
				borderRadius: "12px",
				padding: "8px",
				margin: "5px",
				backgroundColor: "#e5dada",
			  }}
          >
            <img
              src={URL.createObjectURL(gifFile)}
              alt="uploaded-gif"
              style={{ width: "20px", height: "20px", borderRadius: "5px" }}
            />
            <Typography
              sx={{ flex: 1, fontSize: "14px", wordBreak: "break-word" }}
			  >
              {gifFile.name}
            </Typography>
            <IconButton onClick={handleDeleteGif} size="small">
			<span dangerouslySetInnerHTML={{ __html: deletestep }} style={{ zoom: "1" ,display:"flex" }} />
            </IconButton>
        </Box>
    )}
									
									{videoFile && (
        <Box display="flex" alignItems="center" justifyContent="space-between"
            sx={{
                border: "1px solid #0a6",
                borderRadius: "5px",
                padding: "8px",
                marginBottom: "5px",
                width: "196px",
                backgroundColor: "#e6ffe6",
            }}>
            <video width="40" height="40" controls>
                <source src={URL.createObjectURL(videoFile)} type="video/mp4" />
                Your browser does not support the video tag.
            </video>
            <Typography sx={{ flex: 1, ml: 2, fontSize: "14px", wordBreak: "break-word" }}>
                {videoFile.name}
            </Typography>
            <IconButton onClick={handleDeleteVideo} size="small">
                <span dangerouslySetInnerHTML={{ __html: deletestep }} style={{ zoom: 0.7 }} />
            </IconButton>
      </Box>
    )}
    </Box>
    </Box>

				


							<Box
									id="qadpt-designpopup"
									className="qadpt-control-box qadpt-chkcontrol-box"
								>
								<Typography className="qadpt-control-label" sx={{ paddingBottom: "8 !important" }}>{translate("Media Title")}</Typography>

									<TextField
											variant="outlined"
                                            size="small"
									placeholder={translate("Media Title")}
                                            className="qadpt-control-input"
											style={{width: "100%"}}
									value={checklistCheckpointListProperties.mediaTitle}
											onChange={(e) => onPropertyChange("mediaTitle", e.target.value)}
											InputProps={{
												endAdornment: "",
												sx: {
								 
													"&:hover .MuiOutlinedInput-notchedOutline": { border: "none" }, 
													"&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "none" },
													"& fieldset":{border:"none"},
													"& input": { textAlign: "left !important" ,paddingLeft:"10px !important"},
													"&.MuiInputBase-root":{height:"auto !important"}
												},
											}}
										/>
							</Box>
							

							<Box
									id="qadpt-designpopup"
									className="qadpt-control-box qadpt-chkcontrol-box"
								>
								<Typography className="qadpt-control-label" sx={{ paddingBottom: "8px !important" }}>{translate("Media Description")}</Typography>

								<TextField
									value={checklistCheckpointListProperties.mediaDescription}
									onChange={(e) => {
										let value = e.target.value;
										if (value.length > 200) {
											value = value.slice(0, 200);
										}
										onPropertyChange("mediaDescription", value);
									}}
											variant="outlined"
                                            size="small"
									placeholder={translate("Media Desc")}
									className="qadpt-control-input"
									multiline
									minRows={3}
									style={{width: "100%"}}
									helperText={`${checklistCheckpointListProperties.mediaDescription?.length || 0}/200`}
									InputProps={{
												endAdornment: "",
												sx: {
													"&:hover .MuiOutlinedInput-notchedOutline": { border: "none" }, 
													"&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "none" },
													"& fieldset":{border:"none"},
													"& input": { textAlign: "left !important" ,paddingLeft:"10px !important"},
													"&.MuiInputBase-root":{height:"auto !important"}
												},
											}}
										/>
							</Box>



						</div>
					</div>


					<div className="qadpt-drawerFooter">

						<Button
							variant="contained"
							onClick={handleApplyChanges}
							className="qadpt-btn"
						>
							Apply
						</Button>
					</div>
                </div>
                
                
			</div>
			</>
		);
};

export default CheckPointAddPopup;
