import React, { useReducer, useState,useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Box, Typography, TextField, Grid, IconButton, Button, InputAdornment, FormControl, InputLabel, Select, MenuItem, SelectChangeEvent, FormControlLabel, Switch } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import useDrawerStore, { BUTTON_CONT_DEF_VALUE_1, CANVAS_DEFAULT_VALUE, IMG_CONT_DEF_VALUE } from "../../store/drawerStore";
import { HOTSPOT_DEFAULT_VALUE } from "../../store/drawerStore";
import {
  InfoFilled,
  QuestionFill,
  Reselect,
  Solid,
  warning,
} from "../../assets/icons/icons";
import ArrowBackIosNewOutlinedIcon from "@mui/icons-material/ArrowBackIosNewOutlined";

const TitleSubTitle = ({ currentGuide,onBack,onClose }: any) => {
	const { t: translate } = useTranslation();
    const {
			titlePopup,
			setTitlePopup,
			setDesignPopup,
			titleColor,
			setTitleColor,
			checklistTitle,
			setChecklistTitle,
			checklistSubTitle,
			setChecklistSubTitle,
			checklistGuideMetaData,
			updateChecklistTitleSubTitle,
			setIsUnSavedChanges,
			isUnSavedChanges,
		} = useDrawerStore((state: any) => state);
		const [titleError, setTitleError] = useState(""); // Store error message
		const [subtitleError, setsubTitleError] = useState(""); // Store error message
		const [isDisabled, setIsDisabled] = useState(true);
		const [hasChanges, setHasChanges] = useState(false); // Track if any changes were made

	
function getLocalizedDefaults(t: any, existing: any = {}): any {
  const defaultTitle = "Checklist Title";
  const defaultSubTitle = "Context about the tasks in the checklist below users should prioritize completing.";

  return {
    ...existing,
    title:
      existing?.title === defaultTitle || !existing?.title
        ? t(defaultTitle, { defaultValue: defaultTitle })
        : existing?.title,
    subTitle:
      existing?.subTitle === defaultSubTitle || !existing?.subTitle
        ? t(defaultSubTitle, { defaultValue: defaultSubTitle })
        : existing?.subTitle,
    titleColor: existing?.titleColor || "#333",
    titleBold: existing?.titleBold ?? true,
    titleItalic: existing?.titleItalic ?? false,
    subTitleColor: existing?.subTitleColor || "#8D8D8D",
    subTitleBold: existing?.subTitleBold ?? false,
    subTitleItalic: existing?.subTitleItalic ?? false,
  };
}
	
		// Function to check if the Apply button should be enabled
		const updateApplyButtonState = (titleErr: string, subtitleErr: string, changed: boolean) => {
			// Enable the button if there are no errors AND changes have been made
			setIsDisabled(!!titleErr || !!subtitleErr || !changed);
		};

		const handleTitleChange = (e: any) => {
			const value = e.target.value;
			let errorMessage = "";

			if (value.length < 5) {
				errorMessage = translate("Min: 5 Characters");
			} else if (value.length > 50) {
				errorMessage = translate("Max: 50 Characters");
			}

			setTitleError(errorMessage);
			setHasChanges(true); // Mark that changes have been made
			updateApplyButtonState(errorMessage, subtitleError, true);
			onPropertyChange("title", value);
		};

		const handleSubTitleChange = (e: any) => {
			const value = e.target.value;
			let errorMessage = "";

			if (value.length < 5) {
				errorMessage = translate("Min: 5 Characters");
			} else if (value.length > 500) {
				errorMessage = translate("Max: 500 Characters");
			}

			setsubTitleError(errorMessage);
			setHasChanges(true); // Mark that changes have been made
			updateApplyButtonState(titleError, errorMessage, true);
			onPropertyChange("subTitle", value);
		};

		const [titleSubTitleProperties, setTitleSubTitleProperties] = useState(() =>
			getLocalizedDefaults(translate, checklistGuideMetaData[0]?.TitleSubTitle)
);

		const [tempTitle, setTempTitle] = useState(titleSubTitleProperties?.title);
		const [tempSubTitle, settempTempTitle] = useState(titleSubTitleProperties?.subTitle);

		// Store the initial state to compare for changes
		const [initialState, setInitialState] = useState(titleSubTitleProperties);

		// Effect to check for any changes compared to initial state
		useEffect(() => {
			// Compare current properties with initial state
			const hasAnyChanges = Object.keys(titleSubTitleProperties).some(
				(key) => titleSubTitleProperties[key] !== initialState[key]
			);

			setHasChanges(hasAnyChanges);
			updateApplyButtonState(titleError, subtitleError, hasAnyChanges);
		}, [titleSubTitleProperties, initialState, titleError, subtitleError]);
		const handleTitleColorChange = (e: any) => setTitleColor(e.target.value);

		const onPropertyChange = (key: any, value: any) => {
			setTitleSubTitleProperties((prevState: any) => {
				// Check if the value has actually changed
				if (prevState[key] !== value) {
					setHasChanges(true);
					updateApplyButtonState(titleError, subtitleError, true);
				}

				return {
					...prevState,
					[key]: value,
				};
			});
		};

		const handleApplyChanges = () => {
			updateChecklistTitleSubTitle(titleSubTitleProperties);
			// Update the initial state to the current state after applying changes
			setInitialState({ ...titleSubTitleProperties });
			// Reset the changes flag
			setHasChanges(false);
			// Disable the Apply button
			setIsDisabled(true);
			// Close the popup
			setTitlePopup(false);
			// Mark changes as unsaved
			setIsUnSavedChanges(true);
		};

		const handleClose = () => {
			setTitlePopup(false);
		};
		const handledesignclose = () => {
			setDesignPopup(false);
		};
		const handleBlur = () => {
			// If the title is empty, restore the previous title
			if (titleSubTitleProperties.title.trim() === "") {
				setTitleSubTitleProperties((prevState: any) => ({
					...prevState,
					title: tempTitle, // Reset to the default value
				}));
			}
			if (titleSubTitleProperties.subTitle.trim() === "") {
				setTitleSubTitleProperties((prevState: any) => ({
					...prevState,
					subTitle: tempSubTitle, // Reset to the default value
				}));
			}
		};

		return (
			<div
				id="qadpt-designpopup"
				className="qadpt-designpopup"
			>
				<div className="qadpt-content">
					<div className="qadpt-design-header">
						<IconButton
							aria-label="back"
							onClick={onBack}
						>
							<ArrowBackIosNewOutlinedIcon />
						</IconButton>
						<div className="qadpt-title">{translate("Title & SubTitle", { defaultValue: "Title & SubTitle" })}</div>
						<IconButton
							size="small"
							aria-label="close"
							onClick={onClose}
						>
							<CloseIcon />
						</IconButton>
					</div>
					<div className="qadpt-canblock">
						<div className="qadpt-controls qadpt-errmsg">
							<Box
								sx={{
									backgroundColor: "#EAE2E2",
									borderRadius: "var(--button-border-radius)",
									padding: "8px",
									marginBottom: "5px",
								}}
							>
								<Typography sx={{ fontWeight: "600", paddingBottom: "5px", textAlign: "left" }}>{translate("Title", { defaultValue: "Title" })}</Typography>

								<Box
									className="qadpt-control-box"
									sx={{ padding: "0 !important", height: "auto !important" }}
								>
									<TextField
										variant="outlined"
										size="small"
										placeholder={translate("Checklist Title", { defaultValue: "Checklist Title" })}
										className="qadpt-control-input"
										value={titleSubTitleProperties.title}
										style={{ width: "100%" }}
										onChange={handleTitleChange}
										error={Boolean(titleError)} // Show error if message exists
										helperText={
											titleError ? (
												<span style={{ display: "flex", fontSize: "12px", alignItems: "center" }}>
													<span
														style={{ marginRight: "4px" }}
														dangerouslySetInnerHTML={{ __html: warning }}
													/>
													{translate(titleError, { defaultValue: titleError })}
												</span>
											) : null
										}
										InputProps={{
											sx: {
												"&:hover .MuiOutlinedInput-notchedOutline": { border: "none" },
												"&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "none" },
												"& fieldset": { border: "none" },
												"& input": { textAlign: "left !important", paddingLeft: "10px !important" },
												"&.MuiInputBase-root": { height: "auto !important", marginBottom: "5px" },
											},
										}}
									/>
								</Box>

								<Box
									className="qadpt-control-box"
									sx={{
										backgroundColor: "#E5DADA !important",
										height: "35px !important",
										borderRadius: "6px !important",
									}}
								>
									<div className="qadpt-control-label">{translate("Title Color", { defaultValue: "Title Color" })}</div>
									<div>
										<input
											type="color"
											value={titleSubTitleProperties.titleColor}
											onChange={(e) => onPropertyChange("titleColor", e.target.value)}
											className="qadpt-color-input"
										/>
									</div>
								</Box>

								<Box
									className="qadpt-control-box"
									sx={{
										backgroundColor: "#E5DADA !important",
										height: "35px !important",
										borderRadius: "6px !important",
									}}
								>
									<div className="qadpt-control-label">{translate("Bold", { defaultValue: "Bold" })}</div>
									<div>
										<label className="toggle-switch qadpt-toggle-group">
											<input
												type="checkbox"
												checked={titleSubTitleProperties.titleBold}
												onChange={(e) => onPropertyChange("titleBold", e.target.checked)}
												name="toggleSwitch"
											/>
											<span className="slider"></span>
										</label>
									</div>
								</Box>

								<Box
									className="qadpt-control-box"
									sx={{
										backgroundColor: "#E5DADA !important",
										height: "35px !important",
										borderRadius: "6px !important",
									}}
								>
									<div className="qadpt-control-label">{translate("Italic", { defaultValue: "Italic" })} </div>
									<div>
										<label className="toggle-switch qadpt-toggle-group">
											<input
												type="checkbox"
												checked={titleSubTitleProperties.titleItalic}
												onChange={(e) => onPropertyChange("titleItalic", e.target.checked)}
												name="toggleSwitch"
												//disabled={(selectedTemplate === "Tooltip" || selectedTemplateTour === "Tooltip") && status}
											/>
											<span className="slider"></span>
										</label>
									</div>
								</Box>
							</Box>

							<Box
								sx={{
									backgroundColor: "#EAE2E2",
									borderRadius: "var(--button-border-radius)",
									height: "auto",
									padding: "8px",
									marginBottom: "5px",
								}}
							>
								<Typography sx={{ fontWeight: "600", paddingBottom: "5px", textAlign: "left" }}>{translate("Sub Title", { defaultValue: "Sub Title" })}</Typography>

								<Box
									className="qadpt-control-box"
									sx={{ padding: "0 !important", height: "auto !important" }}
								>
									<TextField
										variant="outlined"
										size="small"
										placeholder={translate("Checklist Title", { defaultValue: "Checklist Title" })}
										className="qadpt-control-input"
										value={titleSubTitleProperties.subTitle}
										style={{ width: "100%" }}
										onChange={handleSubTitleChange}
										error={Boolean(subtitleError)} // Show error if message exists
										helperText={
											subtitleError ? (
												<span style={{ display: "flex", fontSize: "12px", alignItems: "center" }}>
													<span
														style={{ marginRight: "4px" }}
														dangerouslySetInnerHTML={{ __html: warning }}
													/>
													{translate(subtitleError, { defaultValue: subtitleError })}
												</span>
											) : null
										}
										InputProps={{
											endAdornment: "",
											sx: {
												"&:hover .MuiOutlinedInput-notchedOutline": { border: "none" },
												"&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "none" },
												"& fieldset": { border: "none" },
												"& input": { textAlign: "left !important", paddingLeft: "10px !important" },
												"&.MuiInputBase-root": { height: "auto !important", marginBottom: "5px" },
											},
										}}
									/>
								</Box>

								<Box
									className="qadpt-control-box"
									sx={{
										backgroundColor: "#E5DADA !important",
										height: "35px !important",
										borderRadius: "6px !important",
									}}
								>
									<div className="qadpt-control-label">{translate("SubTitle Color", { defaultValue: "SubTitle Color" })}</div>
									<div>
										<input
											type="color"
											value={titleSubTitleProperties.subTitleColor}
											onChange={(e) => onPropertyChange("subTitleColor", e.target.value)}
											className="qadpt-color-input"
										/>
									</div>
								</Box>

								<Box
									className="qadpt-control-box"
									sx={{
										backgroundColor: "#E5DADA !important",
										height: "35px !important",
										borderRadius: "6px !important",
									}}
								>
									<div className="qadpt-control-label">{translate("Bold", { defaultValue: "Bold" })}</div>
									<div>
										<label className="toggle-switch ">
											<input
												type="checkbox"
												checked={titleSubTitleProperties.subTitleBold}
												onChange={(e) => onPropertyChange("subTitleBold", e.target.checked)}
												name="toggleSwitch"
											/>
											<span className="slider"></span>
										</label>
									</div>
								</Box>

								<Box
									className="qadpt-control-box"
									sx={{
										backgroundColor: "#E5DADA !important",
										height: "35px !important",
										borderRadius: "6px !important",
									}}
								>
									<div className="qadpt-control-label">{translate("Italic", { defaultValue: "Italic" })} </div>
									<div>
										<label className="toggle-switch qadpt-toggle-group">
											<input
												type="checkbox"
												checked={titleSubTitleProperties.subTitleItalic}
												onChange={(e) => onPropertyChange("subTitleItalic", e.target.checked)}
												name="toggleSwitch"
											/>
											<span className="slider"></span>
										</label>
									</div>
								</Box>
							</Box>
						</div>
					</div>
					<div className="qadpt-drawerFooter">
						<Button
							variant="contained"
							onClick={handleApplyChanges}
							className={`qadpt-btn ${isDisabled ? "disabled" : ""}`}
							disabled={isDisabled}
						>
							{translate("Apply", { defaultValue: "Apply" })}
						</Button>
					</div>
				</div>
			</div>
		);
};

export default TitleSubTitle;
