import React, { useEffect, useState } from "react";
import { <PERSON>, Typo<PERSON>, TextField, Grid, IconButton, Button, Tooltip, Switch } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import ArrowBackIosNewOutlinedIcon from "@mui/icons-material/ArrowBackIosNewOutlined";
import {
	AlignHorizontalLeft as TopLeftIcon,
	AlignHorizontalCenter as TopCenterIcon,
	AlignHorizontalRight as TopRightIcon,
	AlignVerticalTop as MiddleLeftIcon,
	AlignVerticalCenter as MiddleCenterIcon,
	AlignVerticalBottom as MiddleRightIcon,
	AlignHorizontalLeft as BottomLeftIcon,
	AlignHorizontalCenter as BottomCenterIcon,
	AlignHorizontalRight as BottomRightIcon,
	Padding,
} from "@mui/icons-material";
import "./Canvas.module.css";
import useDrawerStore, { CANVAS_DEFAULT_VALUE, CANVAS_DEFAULT_VALUE_HOTSPOT, TCan<PERSON> } from "../../../store/drawerStore";
import { tooltipLeft, tooltipRight, tooltipTop, tooltipBottom, tooltipCenter, warning } from "../../../assets/icons/icons";
import { TouchAppSharp } from "@mui/icons-material";
import { useTranslation } from "react-i18next";

const TooltipCanvasSettings = ({ zindeex, setZindeex, setShowTooltipCanvasSettings,onBack,onClose }: any) => {
	const { t: translate } = useTranslation();
	const {
		setTooltipXaxis,
		setTooltipYaxis,
		updateCanvasInTooltip,
		tooltipXaxis,
		tooltipYaxis,
		tooltipWidth,
		setTooltipWidth,
		setTooltipPadding,
		setTooltipBorderradius,
		setTooltipBordersize,
		setTooltipBordercolor,
		setTooltipBackgroundcolor,
		tooltippadding,
		tooltipborderradius,
		tooltipbordersize,
		tooltipBordercolor,
		tooltipBackgroundcolor,
		tooltipPosition,
		setTooltipPosition,
		setElementSelected,
		setIsTooltipPopup,
		toolTipGuideMetaData,
		currentStep,
		autoPosition,
		setAutoPosition,
		selectedTemplate,
		updateDesignelementInTooltip,
		selectedTemplateTour,
		CANVAS_DEFAULT_VALUE_HOTSPOT,
		setIsUnSavedChanges
	} = useDrawerStore((state: any) => state);

	const [isOpen, setIsOpen] = useState(true);
	const [dismiss, setDismiss] = useState(false);
	const [selectedPosition, setSelectedPosition] = useState("middle-center");
	const [error, setError] = useState(false);
	const [paddingError, setPaddingError] = useState(false);
	const [cornerRadiusError, setCornerRadiusError] = useState(false);
	const [borderSizeError, setBorderSizeError] = useState(false);
	const [tempBorderColor, setTempBorderColor] = useState(tooltipBordercolor);

	useEffect(() => {
		// Sync tempBorderColor with store, using default if empty or transparent
		const validColor = tooltipBordercolor && tooltipBordercolor !== "transparent" && tooltipBordercolor !== "" ? tooltipBordercolor : "#000000";
		setTempBorderColor(validColor);
	  }, [tooltipBordercolor]);

	const positions = [
		{ label: translate("Top Left", { defaultValue: "Top Left" }), icon: <TopLeftIcon fontSize="small" />, value: "top-left" },
		{ label: translate("Top Center", { defaultValue: "Top Center" }), icon: selectedTemplate === "Tooltip" || selectedTemplateTour === "Tooltip" ? <span dangerouslySetInnerHTML={{ __html: tooltipTop }} style={{ fontSize: "small" }} /> : <TopCenterIcon fontSize="small" />, value: "top" },
		{ label: translate("Top Right", { defaultValue: "Top Right" }), icon: <TopRightIcon fontSize="small" />, value: "top-right" },
		{ label: translate("Middle Left", { defaultValue: "Middle Left" }), icon: selectedTemplate === "Tooltip" || selectedTemplateTour === "Tooltip" ? <span dangerouslySetInnerHTML={{ __html: tooltipLeft }} style={{ fontSize: "small" }} /> : <MiddleLeftIcon fontSize="small" />, value: "left" },
		{ label: translate("Middle Center", { defaultValue: "Middle Center" }), icon: selectedTemplate === "Tooltip" || selectedTemplateTour === "Tooltip" ? <span dangerouslySetInnerHTML={{ __html: tooltipCenter }} style={{ fontSize: "small" }} /> : <MiddleCenterIcon fontSize="small" />, value: "center" },
		{ label: translate("Middle Right", { defaultValue: "Middle Right" }), icon: selectedTemplate === "Tooltip" || selectedTemplateTour === "Tooltip" ? <span dangerouslySetInnerHTML={{ __html: tooltipRight }} style={{ fontSize: "small" }} /> : <MiddleRightIcon fontSize="small" />, value: "right" },
		{ label: translate("Bottom Left", { defaultValue: "Bottom Left" }), icon: <BottomLeftIcon fontSize="small" />, value: "bottom-left" },
		{ label: translate("Bottom Center", { defaultValue: "Bottom Center" }), icon: selectedTemplate === "Tooltip" || selectedTemplateTour === "Tooltip" ? <span dangerouslySetInnerHTML={{ __html: tooltipBottom }} style={{ fontSize: "small" }} /> : <BottomCenterIcon fontSize="small" />, value: "bottom" },
		{ label: translate("Bottom Right", { defaultValue: "Bottom Right" }), icon: <BottomRightIcon fontSize="small" />, value: "bottom-right" },
	];

	const handlePositionClick = (e: any) => {
		if (e?.target?.id) {
			//setSelectedPosition(e.target.id);
			setTooltipPosition(e.target.id);
		}
	};

	const onReselectElement = () => {

		TooltipCanvasSettings({
			ReSelection: false,
			XPosition: 4,
			YPosition: 4,
			width: "300",
			Padding: "2",
			borderradius: "8",
			bordersize: "0",
			borderColor: "",
			backgroundColor: "",
			// PulseAnimation: true,
			// stopAnimationUponInteraction: true,
			// ShowUpon: "Hovering Hotspot",
			ShowByDefault: false,
		});
		updateCanvasInTooltip(CANVAS_DEFAULT_VALUE);
		setElementSelected(true);
		setIsTooltipPopup(false);
		setShowTooltipCanvasSettings(false);
	};
	const handleBorderColorChange = (e: any) => {
		if (e?.target?.value) {
			setTempBorderColor(e.target.value);

		}
	};

	const handleBackgroundColorChange = (e: any) => {
		if (e?.target?.value) {
			setTooltipBackgroundcolor(e.target.value);
		}
	};
	const handleAutoSelect = (e: any) => {
		//	setDismiss(e.target.checked);
		setAutoPosition(e.target.checked);
	};

	const handleClose = () => {
		setIsOpen(false);
		setShowTooltipCanvasSettings(false);
	};

	const handleApplyChanges = () => {
		// Create the new canvas settings
		const updatedCanvasSettings = {
			position: tooltipPosition,
			backgroundColor: tooltipBackgroundcolor,
			width: tooltipWidth,
			borderRadius: tooltipborderradius,
			padding: tooltippadding,
			borderColor: tempBorderColor,
			borderSize: tooltipbordersize,
			autoposition: autoPosition,
			xaxis: tooltipXaxis,
			yaxis: tooltipYaxis,
		};

		// Apply the changes - updateCanvasInTooltip will handle recording the change for undo/redo
		updateCanvasInTooltip(updatedCanvasSettings); // Updates canvas and tooltipBordercolor
		handleClose();
		setIsUnSavedChanges(true);
	};

	useEffect(() => {
		if (toolTipGuideMetaData[currentStep - 1]?.canvas) {
			const canvasData = toolTipGuideMetaData[currentStep - 1].canvas;

			// Handle border color - use default if empty, transparent, or invalid
			const borderColor = canvasData?.borderColor;
			const validBorderColor = borderColor && borderColor !== "transparent" && borderColor !== "" ? borderColor : "#000000";

			setTooltipPosition(canvasData?.position || "middle-center");
			setTooltipPadding(canvasData?.padding || "10px");
			setTooltipBorderradius(canvasData?.borderRadius || "8px");
			setTooltipBordersize(canvasData?.borderSize || "0px");
			setTooltipBordercolor(borderColor || "");
			setTempBorderColor(validBorderColor); // Use valid color for the input
			setTooltipBackgroundcolor(canvasData?.backgroundColor || "#FFFFFF");
			setTooltipWidth(canvasData?.width || "300px");
			if (selectedTemplate === "Tooltip" || selectedTemplateTour === "Tooltip") {
				setTooltipXaxis(canvasData?.xaxis || "2px");
				setTooltipYaxis(canvasData?.yaxis || "2px");
			}
			else {
				setTooltipXaxis(canvasData?.xaxis || "100px");
				setTooltipYaxis(canvasData?.yaxis || "100px");

			}
		}
	}, [toolTipGuideMetaData[currentStep - 1]?.canvas]);

	if (!isOpen) return null;

	const formatValueWithPixelOrPercentage = (value: string) => {
		const v = String(value);
		let newValue = v;
		if (v?.endsWith("px") || v?.endsWith("%")) {
			newValue = v.split(/px|%/)[0];
		}
		return newValue;
	};
	const handleChange = (e: any) => {
		// Only allow numeric input
		const value = e.target.value;
		if (!/^-?\d*$/.test(value)) {
			return;
		}
		let inputValue = parseInt(value) || 0;

		setTooltipWidth(`${inputValue}px`);
	};



	return (
		<div
			id="qadpt-designpopup"
			className="qadpt-designpopup"
		>
			<div className="qadpt-content">
				<div className="qadpt-design-header">
					<IconButton
						aria-label="back"
						onClick={onBack}
					>
						<ArrowBackIosNewOutlinedIcon />
					</IconButton>
					<div className="qadpt-title">{(selectedTemplate === "Tooltip" || selectedTemplateTour === "Tooltip") ? translate("Canvas") : translate("Canvas")}</div>
					<IconButton
						size="small"
						aria-label="close"
						onClick={onClose}
					>
						<CloseIcon />
					</IconButton>
				</div>
				<div className="qadpt-canblock">
					<div className="qadpt-controls">
						<Box className="qadpt-control-box">
							{/* <Button
									className="qadpt-design-btn"
									onClick={onReselectElement}
									//startIcon={<DesignServicesIcon />}
									endIcon={<TouchAppSharp />}
								>
									Reselect Element
								</Button> */}

							<div
								className="qadpt-control-label"

							>
								{translate("Auto Position")}		
							</div>
							<div>
							<label className="toggle-switch">
    <input
        type="checkbox"
        checked={autoPosition}
        onChange={handleAutoSelect}
        name="autoPosition"
    />
    <span className="slider"></span>
								</label>
								</div>
						</Box>


						<Box className="qadpt-position-grid"
							sx={{
								opacity: selectedTemplate === "Hotspot" || selectedTemplateTour === "Hotspot"? 0.5 : 1,
								cursor:selectedTemplate === "Hotspot" || selectedTemplateTour === "Hotspot"?"not-allowed":"",
							}}
						>
							<Typography className="qadpt-ctrl-title"
							>{translate("Position")}</Typography>
							<Grid container spacing={1}>
  <Grid item xs={4}>
    <IconButton
      size="small"
      disabled={autoPosition}
      sx={{
        opacity: selectedPosition === positions[0].value ? 1 : 0.5,
      }}
    >
      {/* Top Left - Keep empty or use default MUI icon */}
    </IconButton>
  </Grid>
  <Grid item xs={4}>
    <IconButton
      size="small"
      id={positions[1].value}
      onClick={() => {

        //setSelectedPosition(positions[1].value);
        setTooltipPosition(positions[1].value);
      }}
	disabled={autoPosition || selectedTemplate === "Hotspot" || selectedTemplateTour === "Hotspot"}
	disableRipple
      sx={{
        opacity: tooltipPosition === positions[1].value ? 1 : 0.5,
      }}
    >
      {(selectedTemplate === "Tooltip" || selectedTemplateTour === "Tooltip") ? (
        <span dangerouslySetInnerHTML={{ __html: tooltipTop }}  id={positions[1].value} />
      ) : (
        <TopCenterIcon fontSize="small" />
      )}
    </IconButton>
  </Grid>
  <Grid item xs={4}>
    <IconButton
      size="small"
      disabled={autoPosition}
      sx={{
        opacity: selectedPosition === positions[2].value ? 1 : 0.5,
      }}
    >
      {/* Top Right - Keep empty or use default MUI icon */}
    </IconButton>
  </Grid>
  <Grid item xs={4}>
    <IconButton
      size="small"
      id={positions[3].value}
      onClick={() => {
       // setSelectedPosition(positions[3].value);
        setTooltipPosition(positions[3].value);
      }}
      disabled={autoPosition}
	  disableRipple
      sx={{
        opacity: tooltipPosition === positions[3].value ? 1 : 0.5,
      }}
    >
      {(selectedTemplate === "Tooltip" || selectedTemplateTour === "Tooltip") ? (
        <span dangerouslySetInnerHTML={{ __html: tooltipLeft }} id={positions[3].value} />
      ) : (
        <MiddleLeftIcon fontSize="small" />
      )}
    </IconButton>
  </Grid>
  <Grid item xs={4}>
    <IconButton
      size="small"
      id={positions[4].value}
      onClick={() => {
       // setSelectedPosition(positions[4].value);
        setTooltipPosition(positions[4].value);
      }}
										disabled={autoPosition}
										disableRipple
      sx={{
		  opacity: tooltipPosition === positions[4].value ? 1 : 0.5,
		  paddingLeft:"0 !important"
      }}
    >
      {(selectedTemplate === "Tooltip" || selectedTemplateTour === "Tooltip") ? (
        <span dangerouslySetInnerHTML={{ __html: tooltipCenter }} id={positions[4].value}/>
      ) : (
        <MiddleCenterIcon fontSize="small" />
      )}
    </IconButton>
  </Grid>
  <Grid item xs={4}>
    <IconButton
      size="small"
      id={positions[5].value}
      onClick={() => {
       // setSelectedPosition(positions[5].value);
        setTooltipPosition(positions[5].value);
      }}
      disabled={autoPosition}
	  disableRipple
      sx={{
        opacity: tooltipPosition === positions[5].value ? 1 : 0.5,
      }}
    >
      {(selectedTemplate === "Tooltip" || selectedTemplateTour === "Tooltip") ? (
        <span dangerouslySetInnerHTML={{ __html: tooltipRight }}  id={positions[5].value} />
      ) : (
        <MiddleRightIcon fontSize="small" />
      )}
    </IconButton>
  </Grid>
  <Grid item xs={4}>
    <IconButton
      size="small"
      disabled={autoPosition}
      sx={{
        opacity: selectedPosition === positions[6].value ? 1 : 0.5,
      }}
    >
      {/* Bottom Left - Keep empty or use default MUI icon */}
    </IconButton>
  </Grid>
  <Grid item xs={4}>
    <IconButton
      size="small"
      id={positions[7].value}
      onClick={() => {
       // setSelectedPosition(positions[7].value);
        setTooltipPosition(positions[7].value);
      }}
      disabled={autoPosition}
	  disableRipple
      sx={{
        opacity: tooltipPosition === positions[7].value ? 1 : 0.5,
      }}
    >
      {(selectedTemplate === "Tooltip" || selectedTemplateTour === "Tooltip") ? (
        <span dangerouslySetInnerHTML={{ __html: tooltipBottom }} id={positions[7].value} />
      ) : (
        <BottomCenterIcon fontSize="small" />
      )}
    </IconButton>
  </Grid>
  <Grid item xs={4}>
    <IconButton
      size="small"
      disabled={autoPosition}
      sx={{
        opacity: selectedPosition === positions[8].value ? 1 : 0.5,
      }}
    >
      {/* Bottom Right - Keep empty or use default MUI icon */}
    </IconButton>
  </Grid>
</Grid>
						</Box>
						{/* Width Control */}

						<Box className="qadpt-control-box">
							<div className="qadpt-control-label">X {translate("Axis Offset")}</div>
							<div>
							<TextField
								variant="outlined"
								value={formatValueWithPixelOrPercentage(tooltipXaxis)}

								size="small"
								className="qadpt-control-input"
								onChange={(e) => setTooltipXaxis(`${parseInt(e.target.value) || 0}px`)}
								InputProps={{
									endAdornment: "px",
									sx: {

										"&:hover .MuiOutlinedInput-notchedOutline": { border: "none" },
										"&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "none" },
										"& fieldset":{border:"none"},

									},
								}}
								disabled={autoPosition}
								/>
								</div>
						</Box>
						<Box className="qadpt-control-box">
							<div className="qadpt-control-label">Y {translate("Axis Offset")}</div>
							<div>
							<TextField
								variant="outlined"
								value={formatValueWithPixelOrPercentage(tooltipYaxis)}

								size="small"
								className="qadpt-control-input"
								onChange={(e) => setTooltipYaxis(`${parseInt(e.target.value) || 0}px`)}
								InputProps={{
									endAdornment: "px",
									sx: {

										"&:hover .MuiOutlinedInput-notchedOutline": { border: "none" },
										"&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "none" },
										"& fieldset":{border:"none"},

									},
								}}
								disabled={autoPosition}
								/>
								</div>
						</Box>

						<Box
							className="qadpt-control-box"
							style={{ flexDirection: "column", height: "auto" }}
						>
							<Box style={{ display: "flex", alignItems: "center", gap: "8px", width: "100%" }}>
								<div
									className="qadpt-control-label"

								>
									{translate("Width")}
								</div>
								<div>
								<TextField
									variant="outlined"
									value={formatValueWithPixelOrPercentage(tooltipWidth)}

									size="small"
									className="qadpt-control-input"
									onChange={handleChange}
									InputProps={{
										endAdornment: "px",
										sx: {

											"&:hover .MuiOutlinedInput-notchedOutline": { border: "none" },
											"&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "none" },
											"& fieldset":{border:"none"},

										},
									}}


									/>
									</div>
							</Box>

						</Box>


						<Box className="qadpt-control-box">
							<div className="qadpt-control-label">{translate("Padding")}</div>
							<div>
							<TextField
								variant="outlined"
								value={formatValueWithPixelOrPercentage(tooltippadding)}

								size="small"
								className="qadpt-control-input"
								onChange={(e) => {
									// Only allow numeric input
									const value = e.target.value;
									if (!/^-?\d*$/.test(value)) {
										return;
									}
									const inputValue = parseInt(value) || 0;
									// Validate padding between 0px and 20px
									if (inputValue < 0 || inputValue > 20) {
										setPaddingError(true);
									} else {
										setPaddingError(false);
									}
									setTooltipPadding(`${inputValue}px`);
								}}
								InputProps={{
									endAdornment: "px",
									sx: {

										"&:hover .MuiOutlinedInput-notchedOutline": { border: "none" },
										"&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "none" },
										"& fieldset":{border:"none"},

									},
								}}
								error={paddingError}
								/>
								</div>
						</Box>
						{paddingError && (
						<Typography
						style={{
							fontSize: "12px",
							color: "#e9a971",
							textAlign: "left",
							top: "100%",
							left: 0,
							marginBottom: "5px",
							display: "flex",
						}}
							><span style={{ display: "flex", fontSize: "12px", alignItems: "center", marginRight:"4px" }}

						dangerouslySetInnerHTML={{ __html: warning }}
					/>
								{translate("Value must be between 0px and 20px.")}
							</Typography>
						)}

						<Box className="qadpt-control-box">
							<div className="qadpt-control-label">{translate("Corner Radius")}</div>
							<div>
							<TextField
								variant="outlined"
								value={formatValueWithPixelOrPercentage(tooltipborderradius)}

								size="small"
								className="qadpt-control-input"
								onChange={(e) => {
									// Only allow numeric input
									const value = e.target.value;
									if (!/^-?\d*$/.test(value)) {
										return;
									}
									const inputValue = parseInt(value) || 0;
									// Validate corner radius between 0px and 20px
									if (inputValue < 0 || inputValue > 20) {
										setCornerRadiusError(true);
									} else {
										setCornerRadiusError(false);
									}
									setTooltipBorderradius(`${inputValue}`);
								}}
								InputProps={{
									endAdornment: "px",
									sx: {

										"&:hover .MuiOutlinedInput-notchedOutline": { border: "none" },
										"&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "none" },
										"& fieldset":{border:"none"},

									},
								}}
								error={cornerRadiusError}
								/>
								</div>
						</Box>
						{cornerRadiusError && (
						<Typography
						style={{
							fontSize: "12px",
							color: "#e9a971",
							textAlign: "left",
							top: "100%",
							left: 0,
							marginBottom: "5px",
							display: "flex",
						}}
							><span style={{ display: "flex", fontSize: "12px", alignItems: "center", marginRight:"4px" }}

						dangerouslySetInnerHTML={{ __html: warning }}
					/>
								{translate("Value must be between 0px and 20px.")}
							</Typography>
						)}


<Box className="qadpt-control-box">
							<div className="qadpt-control-label">{translate("Border Size")}</div>
  <div>
    <TextField
      variant="outlined"
      value={formatValueWithPixelOrPercentage(tooltipbordersize)}
      size="small"
      className="qadpt-control-input"
      onChange={(e) => {
        // Only allow numeric input
        const value = e.target.value;
        if (!/^-?\d*$/.test(value)) {
          return;
        }
        const inputValue = parseInt(value) || 0;
        // Validate border size between 0px and 20px
        if (inputValue < 0 || inputValue > 5) {
          setBorderSizeError(true);
        } else {
          setBorderSizeError(false);
        }
        setTooltipBordersize(`${inputValue}px`);
      }}
      InputProps={{
        endAdornment: "px",
        sx: {
          "&:hover .MuiOutlinedInput-notchedOutline": { border: "none" },
          "&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "none" },
          "& fieldset": { border: "none" },
        },
      }}
      error={borderSizeError}
    />
  </div>
</Box>
{borderSizeError && (
  <Typography
    style={{
      fontSize: "12px",
      color: "#e9a971",
      textAlign: "left",
      top: "100%",
      left: 0,
      marginBottom: "5px",
      display: "flex",
    }}
  >
    <span
      style={{ display: "flex", fontSize: "12px", alignItems: "center", marginRight: "4px" }}
      dangerouslySetInnerHTML={{ __html: warning }}
    />
    Value must be between 0px and 5px.
  </Typography>
)}

{/* Button Selection Dropdown - Only show for Tooltip template */}
{/* {(selectedTemplate === "Tooltip" || selectedTemplateTour === "Tooltip") && (
  <Box className="qadpt-control-box" style={{ flexDirection: "column", height: "auto" }}>
    <Typography className="qadpt-ctrl-title">Selected Button</Typography>
    <Box style={{ width: "100%" }}>
      {toolTipGuideMetaData?.[currentStep - 1]?.containers?.[1]?.buttons ? (
        <Select
          value={
            toolTipGuideMetaData[currentStep - 1].containers[1].buttons.find(
              (button: any) =>
                button.name === toolTipGuideMetaData[currentStep - 1]?.design?.gotoNext?.ButtonName
            )?.id || ""
          }
          onChange={(event) => {
            const selectedValue = event.target.value;
            const selectedButton = toolTipGuideMetaData[currentStep - 1]?.containers[1]?.buttons?.find(
              (button: any) => button.id === selectedValue
            );

            // Set dropdown value and button name
            setDropdownValue(selectedValue);
            setElementButtonName(selectedValue);
            setbtnidss(selectedValue);
            setElementClick("button");

            // Save button ID, button name, and "NextStep" in the Design object's "goToNext" object
            const updatedCanvasSettings = {
              NextStep: "button",
              ButtonId: selectedValue,
              ElementPath: "",
              ButtonName: selectedButton?.name,
            };

            updateDesignelementInTooltip(updatedCanvasSettings);

            // Set the button action to "Next" using updateTooltipButtonAction
            if (selectedButton && selectedButton.id) {
              // Find the container ID for the button
              const containerId = toolTipGuideMetaData[currentStep - 1]?.containers[1]?.id;

              // Update the button action to "Next"
              updateTooltipButtonAction(containerId, selectedValue, {
                value: "Next",
                targetURL: "",
                tab: "same-tab",
                interaction: null,
              });

              // Set the selected action to "Next" in the UI
              setSelectActions("Next");

              // Mark changes as unsaved
              setIsUnSavedChanges(true);
            }
          }}
          displayEmpty
          style={{ width: "100%" }}
          sx={{
            "& .MuiSelect-select": {
              textAlign: "left",
              padding: "9px 14px !important",
            },
            "& .MuiSvgIcon-root": {
              height: "20px",
              width: "20px",
              top: "10px",
            },
          }}
        >
          <MenuItem value="" disabled>
            Select a button
          </MenuItem>
          {toolTipGuideMetaData[currentStep - 1].containers[1].buttons.map(
            (button: any, buttonIndex: number) => (
              <MenuItem key={buttonIndex} value={button.id}>
                {button.name}
              </MenuItem>
            )
          )}
        </Select>
      ) : (
        <Typography variant="body2" color="textSecondary">
          No buttons available
        </Typography>
      )}
    </Box>
  </Box>
)} */}

						<Box className="qadpt-control-box">
							<div className="qadpt-control-label">{translate("Border")}</div>
							<div>
							<input
								type="color"
								value={tempBorderColor || "#000000"}
								onChange={handleBorderColorChange}
								className="qadpt-color-input"
								/>
								</div>
						</Box>

						<Box className="qadpt-control-box">
							<div className="qadpt-control-label">{translate("Background")}</div>
							<div>
							<input
								type="color"
								value={tooltipBackgroundcolor}
								onChange={handleBackgroundColorChange}
								className="qadpt-color-input"
								/>
								</div>
						</Box>
					</div>
				</div>
				<div className="qadpt-drawerFooter">
				<Button
				variant="contained"
				onClick={handleApplyChanges}
				className={`qadpt-btn ${paddingError || cornerRadiusError || borderSizeError ? "disabled" : ""}`}
				disabled={paddingError || cornerRadiusError || borderSizeError} // Disable button if any validation errors exist
				>
						{translate("Apply")}
				</Button>

				</div>
			</div>
		</div>
	);
};

export default TooltipCanvasSettings;
