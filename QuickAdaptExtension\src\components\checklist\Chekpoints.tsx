import React, { useReducer, useState,useEffect, useContext, useRef } from "react";
import { <PERSON>, Typo<PERSON>, <PERSON>Field, Grid, IconButton, Button, InputAdornment, FormControl, InputLabel, Select, MenuItem, SelectChangeEvent, FormControlLabel, Switch, Tooltip } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import useDrawerStore, { BUTTON_CONT_DEF_VALUE_1, CANVAS_DEFAULT_VALUE, IMG_CONT_DEF_VALUE } from "../../store/drawerStore";
import { HOTSPOT_DEFAULT_VALUE } from "../../store/drawerStore";
import DraggableCheckpoint from "./DraggableCheckpoint";
import {
  InfoFilled,
  QuestionFill,
  Reselect,
    Solid,
    editicon,
	deleteicon,
	deletestep,
	editpricol,
	warning
} from "../../assets/icons/icons";
import ArrowBackIosNewOutlinedIcon from "@mui/icons-material/ArrowBackIosNewOutlined";
import CheckPointEditPopup from "./CheckpointEditPopup";
import AddIcon from "@mui/icons-material/Add";
import CheckPointAddPopup from "./CheckpointAddPopup";
import { getAllGuides } from "../../services/GuideListServices";
import { AccountContext } from "../login/AccountContext";
import { useTranslation } from 'react-i18next';
import '../../styles/rtl_styles.scss';
interface CheckpointsProps {
    onBack: () => void;
    onClose: () => void;
    // add other props as needed
}
let editInteractionName: string;
const Checkpoints = ({ onBack, onClose }: CheckpointsProps) => {
	const { t: translate } = useTranslation();
	const {
		ShowLauncherSettings,
		setShowLauncherSettings,
		setCheckPointsEditPopup,
		checkpointsEditPopup,
		titlePopup,
		setTitlePopup,
		setDesignPopup,
		titleColor,
		setTitleColor,
		checkpointsPopup,
		setCheckPointsPopup,
		checkpointTitleColor,
		setCheckpointTitleColor,
		checkpointTitleDescription,
		setCheckpointTitleDescription,
		checkpointIconColor,
		setCheckpointIconColor,
		setUnlockCheckPointInOrder,
		unlockCheckPointInOrder,
		checkPointMessage,
		setCheckPointMessage,
		setCheckPointsAddPopup,
		checkpointsAddPopup,
		checklistGuideMetaData,
		updateChecklistCheckPoints,
		deleteCheckpoint,
		setIsUnSavedChanges,
		isUnSavedChanges,
	} = useDrawerStore((state: any) => state);
	const { accountId } = useContext(AccountContext);
	const [messageError, setMessageError] = useState<string>("");
	const [checklistCheckpointProperties, setChecklistCheckpointProperties] = useState<any>(() => {
		const initialchecklistCheckpointProperties = checklistGuideMetaData[0]?.checkpoints || {
			checkpointsList: [],
			checkpointTitles: "#333",
			checkpointsDescription: "#8D8D8D",
			checkpointsIcons: "#333",
			unlockCHeckpointInOrder: false,
			message: "complete items in order",
		};
		return initialchecklistCheckpointProperties;
	});
	// State for tracking changes and apply button
	const [isDisabled, setIsDisabled] = useState(true);
	const [hasChanges, setHasChanges] = useState(false);
	const [initialState, setInitialState] = useState(checklistCheckpointProperties);

	// Keep local state in sync with the store
	useEffect(() => {
		if (checklistGuideMetaData[0]?.checkpoints) {
			const newCheckpoints = checklistGuideMetaData[0].checkpoints;
			setChecklistCheckpointProperties(newCheckpoints);
			setInitialState(newCheckpoints);
			setHasChanges(false);
			setIsDisabled(true);
		}
	}, [checklistGuideMetaData[0]?.checkpoints]);
	// Function to check if the Apply button should be enabled
	const updateApplyButtonState = (changed: boolean, hasErrors: boolean = false) => {
		setIsDisabled(!changed || hasErrors);
	};

	// Removed duplicate declaration of messageError

	// Effect to check for any changes compared to initial state
	useEffect(() => {
		// Compare current properties with initial state
		const hasAnyChanges = JSON.stringify(checklistCheckpointProperties) !== JSON.stringify(initialState);
		setHasChanges(hasAnyChanges);

		// Check for validation errors
		const hasValidationErrors = !!messageError;

		updateApplyButtonState(hasAnyChanges, hasValidationErrors);
	}, [checklistCheckpointProperties, initialState, messageError]);
	const [interactions, setInteractions] = useState<any[]>([]);
	const [skip, setSkip] = useState(0);
	const top = 5;
	const [loading, setLoading] = useState(false);
	const dropdownRef = useRef<HTMLDivElement>(null);

	const handleClose = () => {
		setCheckPointsPopup(false);
	};
	const handledesignclose = () => {
		setDesignPopup(false);
	};

	const onReselectElement = () => {};

	const onPropertyChange = (key: any, value: any) => {
		setChecklistCheckpointProperties((prevState: any) => {
			const newState = {
				...prevState,
				[key]: value,
			};
			// Mark that changes have been made
			setHasChanges(true);
			return newState;
		});
	};
	const [kkk, setKKK] = useState(false);
	const handleApplyChanges = () => {
		if (kkk == false) {
			// If no changes were made to the order, use the original list
			checklistCheckpointProperties.checkpointsList = checklistGuideMetaData[0].checkpoints.checkpointsList;
		}

		updateChecklistCheckPoints(checklistCheckpointProperties);
		// Update the initial state to the current state after applying changes
		setInitialState({ ...checklistCheckpointProperties });
		// Reset the changes flag
		setHasChanges(false);
		// Disable the Apply button
		setIsDisabled(true);
		handleClose();
		setIsUnSavedChanges(true);
	};

	const handleEditClick = (id: any) => {
		editInteractionName = id;
		setCheckPointsEditPopup(true);
	};
	const handleDeleteClick = (interaction: any) => {
		setChecklistCheckpointProperties((prevState: any) => ({
			...prevState,
			checkpointsList: prevState.checkpointsList.filter((checkpoint: any) => checkpoint.interaction !== interaction),
		}));
		setHasChanges(true);
		deleteCheckpoint(interaction);
	};
	let checkpoints = checklistCheckpointProperties.checkpointsList;
	const [draggedItemIndex, setDraggedItemIndex] = useState<number | null>(null);

	// Handle drag start
	const handleDragStart = (index: number) => {
		setDraggedItemIndex(index);
	};

	// Handle drag over (allows dropping)
	const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
		event.preventDefault();
	};

	// Handle drop (reorder items)
	const handleDrop = (index: number) => {
		if (draggedItemIndex === null || draggedItemIndex === index) return;

		const updatedCheckpoints = [...checkpoints];
		const [draggedItem] = updatedCheckpoints.splice(draggedItemIndex, 1);
		updatedCheckpoints.splice(index, 0, draggedItem);
		setKKK(true);
		checkpoints = updatedCheckpoints;
		setDraggedItemIndex(null);

		// Update state correctly and mark changes
		setChecklistCheckpointProperties((prevState: any) => ({
			...prevState, // Copy the existing state
			checkpointsList: updatedCheckpoints, // Update only the checkpointsList
		}));
		setHasChanges(true);
	};

	const handleAddCheckpoint = () => {
		setCheckPointsAddPopup(true);
	};

	const handleMessageChange = (e: any) => {
		const value = e.target.value;
		let errorMessage = "";

		if (value.length < 2) {
			errorMessage = translate("Min: 2 Characters");
		} else if (value.length > 30) {
			errorMessage = translate("Max: 30 Characters");
		}

		setMessageError(errorMessage);
		onPropertyChange("message", value);
	};
	return (
		<div
			id="qadpt-designpopup"
			className="qadpt-designpopup"
		>
			<div className="qadpt-content">
				<div className="qadpt-design-header">
					<IconButton
						aria-label="back"
						onClick={onBack}
					>
						<ArrowBackIosNewOutlinedIcon />
					</IconButton>
					<div className="qadpt-title">{translate("Steps")}</div>
					<IconButton
						size="small"
						aria-label="close"
						onClick={onClose}
					>
						<CloseIcon />
					</IconButton>
				</div>
				<div className="qadpt-canblock">
					<div className="qadpt-controls qadpt-errmsg">
						<Box
							sx={{
								backgroundColor: "#EAE2E2",
								borderRadius: "var(--button-border-radius)",
								height: "auto",
								padding: "10px",
								marginBottom: "5px",
							}}
						>
							<Box>
								{checkpoints.map((checkpoint: any, index: number) => (
									<DraggableCheckpoint
										key={checkpoint.id}
										checkpoint={checkpoint}
										index={index}
										handleEditClick={() => handleEditClick(checkpoint?.id)}
										handleDeleteClick={() => handleDeleteClick(checkpoint?.interaction)}
										handleDragStart={handleDragStart}
										handleDragOver={handleDragOver}
										handleDrop={handleDrop}
										isDragging={index === draggedItemIndex}
									/>
								))}
							</Box>
							<button
								onClick={handleAddCheckpoint}
								className="qadpt-memberButton qadpt-check"
								style={{
									width: "100%",
									backgroundColor: "#D3D9DA",
									color: "var(--primarycolor) !important",
									placeContent: "center",
								}}
							>
								<AddIcon  />
								{/* <i className="fal fa-add-plus"></i> */}
								<span>{translate("Add Step")}</span>
							</button>
						</Box>

						<Box className="qadpt-control-box">
							<div className="qadpt-control-label">{translate("Step Title")}</div>
							<div>
								<input
									type="color"
									value={checklistCheckpointProperties.checkpointTitles}
									onChange={(e) => onPropertyChange("checkpointTitles", e.target.value)}
									className="qadpt-color-input"
								/>
							</div>
						</Box>

						<Box className="qadpt-control-box">
							<div className="qadpt-control-label">{translate("Step Description")}</div>

							<div>
								<input
									type="color"
									value={checklistCheckpointProperties?.checkpointsDescription}
									onChange={(e) => onPropertyChange("checkpointsDescription", e.target.value)}
									className="qadpt-color-input"
								/>
							</div>
						</Box>

						<Box className="qadpt-control-box">
							<div className="qadpt-control-label">{translate("Icon Color")}</div>
							<div>
								<input
									type="color"
									value={checklistCheckpointProperties?.checkpointsIcons}
									onChange={(e) => {
										// Update the global Icons color
										onPropertyChange("checkpointsIcons", e.target.value);
									}}
									className="qadpt-color-input"
								/>
							</div>
						</Box>

						<Box
							className="qadpt-control-box"
							sx={{ height: "auto !important", flexDirection: "column" }}
						>
							<div style={{ display: "flex", alignItems: "center" }}>
								<div className="qadpt-control-label">{translate("Unlock Checkpoints in Order")}</div>

								{/* Show by Default Toggle */}
								<div>
									<label className="toggle-switch">
										<input
											type="checkbox"
											checked={checklistCheckpointProperties.unlockCHeckpointInOrder}
											onChange={(e) => onPropertyChange("unlockCHeckpointInOrder", e.target.checked)}
											name="showByDefault"
										/>
										<span className="slider"></span>
									</label>
								</div>
							</div>
							{checklistCheckpointProperties.unlockCHeckpointInOrder === true && (
								<>
									<Box
										sx={{
											backgroundColor: "#EAE2E2",
											borderRadius: "var(--button-border-radius)",
											height: "auto",
											padding: "8px 8px 0 8px",
										}}
									>
										<Typography sx={{ paddingBottom: "5px", textAlign: "left" }}>{translate("Message")}</Typography>

										<Box
											className="qadpt-control-box"
											sx={{ padding: "0 !important", height: "auto !important" }}
										>
											<TextField
												variant="outlined"
												size="small"
												placeholder={translate("Checklist Title")}
												className="qadpt-control-input"
												value={checklistCheckpointProperties.message}
												style={{ width: "100%" }}
												onChange={handleMessageChange}
												error={Boolean(messageError)} // Show error if message exists
												helperText={
													messageError ? (
														<span style={{ display: "flex", fontSize: "12px", alignItems: "center" }}>
															<span
																style={{ marginRight: "4px" }}
																dangerouslySetInnerHTML={{ __html: warning }}
															/>
															{messageError}
														</span>
													) : null
												}
												InputProps={{
													endAdornment: "",
													sx: {
														"&:hover .MuiOutlinedInput-notchedOutline": { border: "none" },
														"&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "none" },
														"& fieldset": { border: "none" },
														"& input": { textAlign: "left !important", paddingLeft: "10px !important" },
														"&.MuiInputBase-root": { height: "auto !important", marginBottom: "5px" },
													},
												}}
											/>
										</Box>
									</Box>
								</>
							)}
						</Box>
					</div>
				</div>
				<div className="qadpt-drawerFooter">
					<Button
						variant="contained"
						onClick={handleApplyChanges}
						className={`qadpt-btn ${isDisabled ? "disabled" : ""}`}
						disabled={isDisabled}
					>
						{translate("Apply")}
					</Button>
				</div>
			</div>

			{checkpointsEditPopup && (
				<>
					<CheckPointEditPopup
						checkpointsEditPopup={checkpointsEditPopup}
						editInteractionName={editInteractionName}
						onClose={onClose}
					/>
				</>
			)}

			{checkpointsAddPopup && (
				<>
					<CheckPointAddPopup
						checklistCheckpointProperties={checklistCheckpointProperties}
						setChecklistCheckpointProperties={setChecklistCheckpointProperties}
						onClose={onClose}
					/>
				</>
			)}
		</div>
	);
};

export default Checkpoints;
export {editInteractionName};
