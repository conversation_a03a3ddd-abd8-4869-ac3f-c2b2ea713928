{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideBanners\\\\selectedpopupfields\\\\PageInteraction.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport \"../../guideDesign/Canvas.module.css\";\nimport { Box, Typography, TextField, IconButton, Button } from \"@mui/material\";\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport useDrawerStore from \"../../../store/drawerStore\";\nimport { warning } from \"../../../assets/icons/icons\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PageInteractions = ({\n  setShowCanvasSettings,\n  resetHeightofBanner\n}) => {\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    selectedTemplate,\n    padding,\n    setPadding,\n    position,\n    setPosition,\n    radius,\n    setRadius,\n    borderSize,\n    setBorderSize,\n    setBorderColor,\n    borderColor,\n    setBackgroundColor,\n    backgroundColor,\n    backgroundC,\n    setBackgroundC,\n    Bposition,\n    setBposition,\n    bpadding,\n    setbPadding,\n    Bbordercolor,\n    setBBorderColor,\n    BborderSize,\n    setBBorderSize,\n    zindeex,\n    setZindeex,\n    sectionColor,\n    setSectionColor,\n    setBannerCanvasSetting,\n    setOverlayEnabled,\n    setIsUnSavedChanges\n  } = useDrawerStore(state => state);\n  const handlePositionChange = pos => {\n    setPosition(pos);\n  };\n  const [tempPadding, setTempPadding] = useState(bpadding);\n  const [tempBorderSize, setTempBorderSize] = useState(BborderSize || \"0\");\n  const [tempZIndex, setTempZIndex] = useState(zindeex);\n  const [tempBorderColor, setTempBorderColor] = useState(Bbordercolor || \"#000000\");\n  const [tempBackgroundColor, setTempBackgroundColor] = useState(backgroundC);\n  const [tempSectionColor, setTempSectionColor] = useState(sectionColor);\n  const [tempPosition, setTempPosition] = useState(Bposition || \"Cover Top\");\n  const [isOpen, setIsOpen] = useState(true);\n  const [isColorChangesDone, setColorChangesDone] = useState(false);\n  const [isPaddingChangeDone, setPaddingChangeDone] = useState(false);\n  const [paddingError, setPaddingError] = useState(false);\n  const [borderSizeError, setBorderSizeError] = useState(false);\n\n  // Ensure temporary states are initialized with correct defaults\n  useEffect(() => {\n    setTempBorderSize(BborderSize || \"0\");\n    setTempBorderColor(Bbordercolor || \"#000000\");\n  }, [BborderSize, Bbordercolor]);\n  const handleApplyChanges = () => {\n    if (paddingError || borderSizeError) {\n      return;\n    }\n    setBposition(tempPosition);\n    setbPadding(tempPadding || \"10\");\n    setBBorderSize(tempBorderSize || \"0\");\n    setBBorderColor(tempBorderColor);\n    setBackgroundC(tempBackgroundColor);\n    setSectionColor(tempSectionColor);\n    setZindeex(tempZIndex);\n    const paddingToUse = isPaddingChangeDone ? bpadding : tempPadding;\n    // Reset height of banner with the correct padding value\n    resetHeightofBanner(tempPosition, parseInt(paddingToUse || \"12\"), parseInt(tempBorderSize || \"0\"), true, parseInt(tempPadding || \"0\"));\n\n    // Create canvas settings object with all properties\n    const canvasSettings = {\n      Position: tempPosition,\n      BackgroundColor: isColorChangesDone ? tempBackgroundColor : undefined,\n      Padding: paddingToUse || \"12\",\n      BorderColor: tempBorderColor,\n      BorderSize: tempBorderSize || \"0\",\n      Zindex: tempZIndex || 9999,\n      sectionColor: tempSectionColor\n    };\n\n    // Remove undefined properties\n    Object.keys(canvasSettings).forEach(key => {\n      if (canvasSettings[key] === undefined) {\n        delete canvasSettings[key];\n      }\n    });\n\n    // Apply canvas settings\n    setBannerCanvasSetting(canvasSettings);\n\n    // Close the panel and mark changes as unsaved\n    handleClose();\n    setIsUnSavedChanges(true);\n  };\n  const resetTempValues = () => {\n    setTempPadding(bpadding);\n    setTempBorderSize(BborderSize);\n    setTempZIndex(zindeex);\n    setTempBorderColor(Bbordercolor);\n    setTempBackgroundColor(backgroundC);\n    setTempSectionColor(sectionColor);\n    setTempPosition(Bposition || \"Cover Top\");\n    setColorChangesDone(false);\n    setPaddingChangeDone(false);\n    setPaddingError(false);\n    setBorderSizeError(false);\n  };\n  const handleClose = () => {\n    resetTempValues();\n    setIsOpen(false);\n    setShowCanvasSettings(false);\n  };\n  if (!isOpen) return null;\n  const buttonStyle = isSelected => ({\n    border: isSelected ? `1px solid var(--primarycolor)` : \"none\",\n    borderRadius: \"20px\",\n    backgroundColor: isSelected ? \"#d3d9d9\" : \"#f1ecec\",\n    color: \"#000\",\n    cursor: \"pointer\",\n    //isSelected ? \"pointer\" : \"not-allowed\",\n    boxShadow: isSelected ? \"0 0 2px rgba(0,0,0,0.2)\" : \"none\",\n    opacity: isSelected ? \"1\" : \"0.5\",\n    padding: \"5px\",\n    lineHeight: \"15px\"\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"qadpt-designpopup\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadpt-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-design-header\",\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          \"aria-label\": \"close\",\n          onClick: handleClose,\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIosNewOutlinedIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-title\",\n          children: translate(\"Canvas\", {\n            defaultValue: \"Canvas\"\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"small\",\n          \"aria-label\": \"close\",\n          onClick: handleClose,\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 6\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          alignItems: \"center\",\n          backgroundColor: \"var(--back-light-color)\",\n          borderRadius: \"var(--button-border-radius)\",\n          height: \"auto\",\n          margin: \"0 15px 5px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            fontWeight: \"600\"\n          },\n          children: translate(\"Position\", {\n            defaultValue: \"Position\"\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: \"flex\",\n            padding: \"8px\",\n            placeContent: \"center\",\n            gap: \"5px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            style: buttonStyle(tempPosition !== \"Cover Top\"),\n            onClick: () => {\n              setTempPosition(\"Push Down\");\n              // When changing position, mark padding as changed to ensure it's applied\n              setPaddingChangeDone(true);\n            },\n            children: translate(\"Push Down\", {\n              defaultValue: \"Push Down\"\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            style: buttonStyle(tempPosition === \"Cover Top\"),\n            onClick: () => {\n              setTempPosition(\"Cover Top\");\n              // When changing position, mark padding as changed to ensure it's applied\n              setPaddingChangeDone(true);\n            },\n            children: translate(\"Cover Top\", {\n              defaultValue: \"Cover Top\"\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 7\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 6\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-controls\",\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          className: \"qadpt-control-box\",\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            className: \"qadpt-control-label\",\n            children: translate(\"Padding\", {\n              defaultValue: \"Padding\"\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            variant: \"outlined\",\n            value: tempPadding || \"0\",\n            fullWidth: true,\n            size: \"small\",\n            className: \"qadpt-control-input\",\n            onChange: e => {\n              const inputValue = parseInt(e.target.value) || 0;\n\n              // Validate padding between 0px and 20px\n              if (inputValue < 0 || inputValue > 20) {\n                setPaddingError(true);\n              } else {\n                setPaddingError(false);\n              }\n              setTempPadding(inputValue.toString());\n              setPaddingChangeDone(true);\n            },\n            InputProps: {\n              endAdornment: \"px\",\n              sx: {\n                \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                  border: \"none\"\n                },\n                \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                  border: \"none\"\n                },\n                \"& fieldset\": {\n                  border: \"none\"\n                }\n              }\n            },\n            error: paddingError\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 7\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 6\n        }, this), paddingError && /*#__PURE__*/_jsxDEV(Typography, {\n          style: {\n            fontSize: \"12px\",\n            color: \"#e9a971\",\n            textAlign: \"left\",\n            top: \"100%\",\n            left: 0,\n            marginBottom: \"5px\",\n            display: \"flex\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              display: \"flex\",\n              fontSize: \"12px\",\n              alignItems: \"center\",\n              marginRight: \"4px\"\n            },\n            dangerouslySetInnerHTML: {\n              __html: warning\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 8\n          }, this), translate(\"padding_range_error\", {\n            defaultValue: \"Value must be between 0px and 20px.\"\n          })]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          className: \"qadpt-control-box\",\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            className: \"qadpt-control-label\",\n            children: translate(\"Border Size\", {\n              defaultValue: \"Border Size\"\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            variant: \"outlined\",\n            value: tempBorderSize || \"0\",\n            fullWidth: true,\n            size: \"small\",\n            className: \"qadpt-control-input\",\n            onChange: e => {\n              const inputValue = parseInt(e.target.value) || 0;\n\n              // Validate border size between 0px and 10px\n              if (inputValue < 0 || inputValue > 10) {\n                setBorderSizeError(true);\n              } else {\n                setBorderSizeError(false);\n              }\n              setTempBorderSize(inputValue.toString());\n            },\n            InputProps: {\n              endAdornment: \"px\",\n              sx: {\n                \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                  border: \"none\"\n                },\n                \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                  border: \"none\"\n                },\n                \"& fieldset\": {\n                  border: \"none\"\n                }\n              }\n            },\n            error: borderSizeError\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 7\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 6\n        }, this), borderSizeError && /*#__PURE__*/_jsxDEV(Typography, {\n          style: {\n            fontSize: \"12px\",\n            color: \"#e9a971\",\n            textAlign: \"left\",\n            top: \"100%\",\n            left: 0,\n            marginBottom: \"5px\",\n            display: \"flex\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              display: \"flex\",\n              fontSize: \"12px\",\n              alignItems: \"center\",\n              marginRight: \"4px\"\n            },\n            dangerouslySetInnerHTML: {\n              __html: warning\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 8\n          }, this), translate(\"border_size_range_error\", {\n            defaultValue: \"Value must be between 0px and 10px.\"\n          })]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          className: \"qadpt-control-box\",\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            className: \"qadpt-control-label\",\n            children: translate(\"Border\", {\n              defaultValue: \"Border\"\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"color\",\n            value: tempBorderColor,\n            onChange: e => setTempBorderColor(e.target.value),\n            className: \"qadpt-color-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 7\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          className: \"qadpt-control-box\",\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            className: \"qadpt-control-label\",\n            children: translate(\"Background\", {\n              defaultValue: \"Background\"\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"color\",\n            value: tempBackgroundColor,\n            onChange: e => {\n              setColorChangesDone(true);\n              setTempBackgroundColor(e.target.value);\n            },\n            className: \"qadpt-color-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 7\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 6\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-drawerFooter\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: handleApplyChanges,\n          className: `qadpt-btn ${paddingError || borderSizeError ? \"disabled\" : \"\"}`,\n          disabled: paddingError || borderSizeError,\n          children: translate(\"Apply\", {\n            defaultValue: \"Apply\"\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 5\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 4\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 151,\n    columnNumber: 3\n  }, this);\n};\n_s(PageInteractions, \"HplKGOU4HLfs3CSzml29tVSbXbk=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c = PageInteractions;\nexport default PageInteractions;\nvar _c;\n$RefreshReg$(_c, \"PageInteractions\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "TextField", "IconButton", "<PERSON><PERSON>", "ArrowBackIosNewOutlinedIcon", "CloseIcon", "useDrawerStore", "warning", "useTranslation", "jsxDEV", "_jsxDEV", "PageInteractions", "setShowCanvasSettings", "resetHeightofBanner", "_s", "t", "translate", "selectedTemplate", "padding", "setPadding", "position", "setPosition", "radius", "setRadius", "borderSize", "setBorderSize", "setBorderColor", "borderColor", "setBackgroundColor", "backgroundColor", "backgroundC", "setBackgroundC", "Bposition", "setBposition", "bpadding", "setbPadding", "Bbordercolor", "setBBorderColor", "BborderSize", "setBBorderSize", "zindeex", "setZindeex", "sectionColor", "setSectionColor", "setBannerCanvasSetting", "setOverlayEnabled", "setIsUnSavedChanges", "state", "handlePositionChange", "pos", "tempPadding", "setTempPadding", "tempBorderSize", "setTempBorderSize", "tempZIndex", "setTempZIndex", "tempBorderColor", "setTempBorderColor", "tempBackgroundColor", "setTempBackgroundColor", "tempSectionColor", "setTempSectionColor", "tempPosition", "setTempPosition", "isOpen", "setIsOpen", "isColorChangesDone", "setColorChangesDone", "isPaddingChangeDone", "setPaddingChangeDone", "paddingError", "setPaddingError", "borderSizeError", "setBorderSizeError", "handleApplyChanges", "paddingToUse", "parseInt", "canvasSettings", "Position", "BackgroundColor", "undefined", "Padding", "BorderColor", "BorderSize", "Zindex", "Object", "keys", "for<PERSON>ach", "key", "handleClose", "resetTempV<PERSON>ues", "buttonStyle", "isSelected", "border", "borderRadius", "color", "cursor", "boxShadow", "opacity", "lineHeight", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "defaultValue", "size", "style", "alignItems", "height", "margin", "fontWeight", "display", "place<PERSON><PERSON>nt", "gap", "variant", "value", "fullWidth", "onChange", "e", "inputValue", "target", "toString", "InputProps", "endAdornment", "sx", "error", "fontSize", "textAlign", "top", "left", "marginBottom", "marginRight", "dangerouslySetInnerHTML", "__html", "type", "disabled", "_c", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/guideBanners/selectedpopupfields/PageInteraction.tsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport \"../../guideDesign/Canvas.module.css\";\r\nimport { Box, Typography, TextField, Grid, IconButton, Tooltip, Button } from \"@mui/material\";\r\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport useDrawerStore from \"../../../store/drawerStore\";\r\nimport { warning } from \"../../../assets/icons/icons\";\r\nimport { useTranslation } from \"react-i18next\";\r\n\r\nconst PageInteractions = ({ setShowCanvasSettings, resetHeightofBanner }: any) => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst {\r\n\t\tselectedTemplate,\r\n\t\tpadding,\r\n\t\tsetPadding,\r\n\t\tposition,\r\n\t\tsetPosition,\r\n\t\tradius,\r\n\t\tsetRadius,\r\n\t\tborderSize,\r\n\t\tsetBorderSize,\r\n\t\tsetBorderColor,\r\n\t\tborderColor,\r\n\t\tsetBackgroundColor,\r\n\t\tbackgroundColor,\r\n\t\tbackgroundC,\r\n\t\tsetBackgroundC,\r\n\t\tBposition,\r\n\t\tsetBposition,\r\n\t\tbpadding,\r\n\t\tsetbPadding,\r\n\t\tBbordercolor,\r\n\t\tsetBBorderColor,\r\n\t\tBborderSize,\r\n\t\tsetBBorderSize,\r\n\t\tzindeex,\r\n\t\tsetZindeex,\r\n\t\tsectionColor,\r\n\t\tsetSectionColor,\r\n\t\tsetBannerCanvasSetting,\r\n\t\tsetOverlayEnabled,\r\n\t\tsetIsUnSavedChanges\r\n\t} = useDrawerStore((state) => state);\r\n\r\n\tconst handlePositionChange = (pos: any) => {\r\n\t\tsetPosition(pos);\r\n\t};\r\n\tconst [tempPadding, setTempPadding] = useState(bpadding);\r\n\tconst [tempBorderSize, setTempBorderSize] = useState(BborderSize || \"0\");\r\n\tconst [tempZIndex, setTempZIndex] = useState(zindeex);\r\n\tconst [tempBorderColor, setTempBorderColor] = useState(Bbordercolor || \"#000000\");\r\n\tconst [tempBackgroundColor, setTempBackgroundColor] = useState(backgroundC);\r\n\tconst [tempSectionColor, setTempSectionColor] = useState(sectionColor);\r\n\tconst [tempPosition, setTempPosition] = useState(Bposition || \"Cover Top\");\r\n\tconst [isOpen, setIsOpen] = useState(true);\r\n\tconst [isColorChangesDone, setColorChangesDone] = useState(false);\r\n\tconst [isPaddingChangeDone, setPaddingChangeDone] = useState(false);\r\n\tconst [paddingError, setPaddingError] = useState(false);\r\n\tconst [borderSizeError, setBorderSizeError] = useState(false);\r\n\r\n\t// Ensure temporary states are initialized with correct defaults\r\n\tuseEffect(() => {\r\n\t\tsetTempBorderSize(BborderSize || \"0\");\r\n\t\tsetTempBorderColor(Bbordercolor || \"#000000\");\r\n\t}, [BborderSize, Bbordercolor]);\r\n\r\n\tconst handleApplyChanges = () => {\r\n\t\tif (paddingError || borderSizeError) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tsetBposition(tempPosition);\r\n\t\tsetbPadding(tempPadding || \"10\");\r\n\t\tsetBBorderSize(tempBorderSize || \"0\");\r\n\t\tsetBBorderColor(tempBorderColor);\r\n\t\tsetBackgroundC(tempBackgroundColor);\r\n\t\tsetSectionColor(tempSectionColor);\r\n\t\tsetZindeex(tempZIndex);\r\n\r\n\t\tconst paddingToUse = isPaddingChangeDone ? bpadding : tempPadding;\r\n\t\t// Reset height of banner with the correct padding value\r\n\t\tresetHeightofBanner(\r\n\t\t\ttempPosition,\r\n\t\t\tparseInt(paddingToUse || \"12\"),\r\n\t\t\tparseInt(tempBorderSize || \"0\"),\r\n\t\t\ttrue,\r\n\t\t\tparseInt(tempPadding || \"0\")\r\n\t\t);\r\n\r\n\t\t// Create canvas settings object with all properties\r\n\t\tconst canvasSettings: Record<string, any> = {\r\n\t\t\tPosition: tempPosition,\r\n\t\t\tBackgroundColor: isColorChangesDone ? tempBackgroundColor : undefined,\r\n\t\t\tPadding: paddingToUse || \"12\",\r\n\t\t\tBorderColor: tempBorderColor,\r\n\t\t\tBorderSize: tempBorderSize || \"0\",\r\n\t\t\tZindex: tempZIndex || 9999,\r\n\t\t\tsectionColor: tempSectionColor,\r\n\t\t};\r\n\r\n\t\t// Remove undefined properties\r\n\t\tObject.keys(canvasSettings).forEach((key) => {\r\n\t\t\tif (canvasSettings[key] === undefined) {\r\n\t\t\t\tdelete canvasSettings[key];\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\t// Apply canvas settings\r\n\t\tsetBannerCanvasSetting(canvasSettings);\r\n\r\n\t\t// Close the panel and mark changes as unsaved\r\n\t\thandleClose();\r\n\t\tsetIsUnSavedChanges(true);\r\n\t};\r\n\r\n\tconst resetTempValues = () => {\r\n\t\tsetTempPadding(bpadding);\r\n\t\tsetTempBorderSize(BborderSize);\r\n\t\tsetTempZIndex(zindeex);\r\n\t\tsetTempBorderColor(Bbordercolor);\r\n\t\tsetTempBackgroundColor(backgroundC);\r\n\t\tsetTempSectionColor(sectionColor);\r\n\t\tsetTempPosition(Bposition || \"Cover Top\");\r\n\t\tsetColorChangesDone(false);\r\n\t\tsetPaddingChangeDone(false);\r\n\t\tsetPaddingError(false);\r\n\t\tsetBorderSizeError(false);\r\n\t};\r\n\r\n\tconst handleClose = () => {\r\n\t\tresetTempValues();\r\n\t\tsetIsOpen(false);\r\n\t\tsetShowCanvasSettings(false);\r\n\t};\r\n\r\n\tif (!isOpen) return null;\r\n\r\n\tconst buttonStyle = (isSelected: boolean) => ({\r\n\t\tborder: isSelected ? `1px solid var(--primarycolor)` : \"none\",\r\n\t\tborderRadius: \"20px\",\r\n\t\tbackgroundColor: isSelected ? \"#d3d9d9\" : \"#f1ecec\",\r\n\t\tcolor: \"#000\",\r\n\t\tcursor: \"pointer\", //isSelected ? \"pointer\" : \"not-allowed\",\r\n\t\tboxShadow: isSelected ? \"0 0 2px rgba(0,0,0,0.2)\" : \"none\",\r\n\t\topacity: isSelected ? \"1\" : \"0.5\",\r\n\t\tpadding: \"5px\",\r\n\t\tlineHeight: \"15px\",\r\n\t});\r\n\r\n\treturn (\r\n\t\t<div className=\"qadpt-designpopup\">\r\n\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<ArrowBackIosNewOutlinedIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t<div className=\"qadpt-title\">{translate(\"Canvas\", { defaultValue: \"Canvas\" })}</div>\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t<div\r\n\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\tbackgroundColor: \"var(--back-light-color)\",\r\n\t\t\t\t\t\tborderRadius: \"var(--button-border-radius)\",\r\n\t\t\t\t\t\theight: \"auto\",\r\n\t\t\t\t\t\tmargin: \"0 15px 5px\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t<label style={{ fontWeight: \"600\" }}>{translate(\"Position\", { defaultValue: \"Position\" })}</label>\r\n\t\t\t\t\t<div style={{ display: \"flex\", padding: \"8px\", placeContent: \"center\", gap: \"5px\" }}>\r\n\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\tstyle={buttonStyle(tempPosition !== \"Cover Top\")}\r\n\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\tsetTempPosition(\"Push Down\");\r\n\t\t\t\t\t\t\t\t// When changing position, mark padding as changed to ensure it's applied\r\n\t\t\t\t\t\t\t\tsetPaddingChangeDone(true);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{translate(\"Push Down\", { defaultValue: \"Push Down\" })}\r\n\t\t\t\t\t\t</button>\r\n\r\n\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\tstyle={buttonStyle(tempPosition === \"Cover Top\")}\r\n\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\tsetTempPosition(\"Cover Top\");\r\n\t\t\t\t\t\t\t\t// When changing position, mark padding as changed to ensure it's applied\r\n\t\t\t\t\t\t\t\tsetPaddingChangeDone(true);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{translate(\"Cover Top\", { defaultValue: \"Cover Top\" })}\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t<div className=\"qadpt-controls\">\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Padding\", { defaultValue: \"Padding\" })}</Typography>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={tempPadding || \"0\"}\r\n\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\r\n\t\t\t\t\t\t\t\tconst inputValue = parseInt(e.target.value) || 0;\r\n\r\n\t\t\t\t\t\t\t\t// Validate padding between 0px and 20px\r\n\t\t\t\t\t\t\t\tif (inputValue < 0 || inputValue > 20) {\r\n\t\t\t\t\t\t\t\t\tsetPaddingError(true);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tsetPaddingError(false);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tsetTempPadding(inputValue.toString());\r\n\t\t\t\t\t\t\t\tsetPaddingChangeDone(true);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"& fieldset\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\terror={paddingError}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t{paddingError && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t><span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight: \"4px\" }}\r\n\r\n\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t{translate(\"padding_range_error\", { defaultValue: \"Value must be between 0px and 20px.\" })}\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t)}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Border Size\", { defaultValue: \"Border Size\" })}</Typography>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={tempBorderSize || \"0\"}\r\n\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\r\n\t\t\t\t\t\t\t\tconst inputValue = parseInt(e.target.value) || 0;\r\n\r\n\t\t\t\t\t\t\t\t// Validate border size between 0px and 10px\r\n\t\t\t\t\t\t\t\tif (inputValue < 0 || inputValue > 10) {\r\n\t\t\t\t\t\t\t\t\tsetBorderSizeError(true);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tsetBorderSizeError(false);\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tsetTempBorderSize(inputValue.toString());\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"& fieldset\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\terror={borderSizeError}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t{borderSizeError && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t><span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight: \"4px\" }}\r\n\r\n\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t{translate(\"border_size_range_error\", { defaultValue: \"Value must be between 0px and 10px.\" })}\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t)}\r\n\t\t\t\t\t{/* <Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">Z-Index</Typography>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={tempZIndex}\r\n\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\tmargin=\"dense\"\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => setTempZIndex(parseInt(e.target.value) || 0)}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Box> */}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Border\", { defaultValue: \"Border\" })}</Typography>\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\tvalue={tempBorderColor}\r\n\t\t\t\t\t\t\tonChange={(e) => setTempBorderColor(e.target.value)}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Background\", { defaultValue: \"Background\" })}</Typography>\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\tvalue={tempBackgroundColor}\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\tsetColorChangesDone(true);\r\n\t\t\t\t\t\t\t\tsetTempBackgroundColor(e.target.value);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t{/* <Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">Section Color</Typography>\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\tvalue={tempSectionColor}\r\n\t\t\t\t\t\t\tonChange={(e) => setTempSectionColor(e.target.value)}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Box> */}\r\n\t\t\t\t</div>\r\n\t\t\t\t<div className=\"qadpt-drawerFooter\">\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\tonClick={handleApplyChanges}\r\n\t\t\t\t\t\tclassName={`qadpt-btn ${paddingError || borderSizeError ? \"disabled\" : \"\"}`}\r\n\t\t\t\t\t\tdisabled={paddingError || borderSizeError}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{translate(\"Apply\", { defaultValue: \"Apply\" })}\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t);\r\n};\r\n\r\nexport default PageInteractions;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,qCAAqC;AAC5C,SAASC,GAAG,EAAEC,UAAU,EAAEC,SAAS,EAAQC,UAAU,EAAWC,MAAM,QAAQ,eAAe;AAC7F,OAAOC,2BAA2B,MAAM,6CAA6C;AACrF,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,cAAc,MAAM,4BAA4B;AACvD,SAASC,OAAO,QAAQ,6BAA6B;AACrD,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,qBAAqB;EAAEC;AAAyB,CAAC,KAAK;EAAAC,EAAA;EACjF,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGR,cAAc,CAAC,CAAC;EACzC,MAAM;IACLS,gBAAgB;IAChBC,OAAO;IACPC,UAAU;IACVC,QAAQ;IACRC,WAAW;IACXC,MAAM;IACNC,SAAS;IACTC,UAAU;IACVC,aAAa;IACbC,cAAc;IACdC,WAAW;IACXC,kBAAkB;IAClBC,eAAe;IACfC,WAAW;IACXC,cAAc;IACdC,SAAS;IACTC,YAAY;IACZC,QAAQ;IACRC,WAAW;IACXC,YAAY;IACZC,eAAe;IACfC,WAAW;IACXC,cAAc;IACdC,OAAO;IACPC,UAAU;IACVC,YAAY;IACZC,eAAe;IACfC,sBAAsB;IACtBC,iBAAiB;IACjBC;EACD,CAAC,GAAGxC,cAAc,CAAEyC,KAAK,IAAKA,KAAK,CAAC;EAEpC,MAAMC,oBAAoB,GAAIC,GAAQ,IAAK;IAC1C5B,WAAW,CAAC4B,GAAG,CAAC;EACjB,CAAC;EACD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGtD,QAAQ,CAACqC,QAAQ,CAAC;EACxD,MAAM,CAACkB,cAAc,EAAEC,iBAAiB,CAAC,GAAGxD,QAAQ,CAACyC,WAAW,IAAI,GAAG,CAAC;EACxE,MAAM,CAACgB,UAAU,EAAEC,aAAa,CAAC,GAAG1D,QAAQ,CAAC2C,OAAO,CAAC;EACrD,MAAM,CAACgB,eAAe,EAAEC,kBAAkB,CAAC,GAAG5D,QAAQ,CAACuC,YAAY,IAAI,SAAS,CAAC;EACjF,MAAM,CAACsB,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG9D,QAAQ,CAACiC,WAAW,CAAC;EAC3E,MAAM,CAAC8B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhE,QAAQ,CAAC6C,YAAY,CAAC;EACtE,MAAM,CAACoB,YAAY,EAAEC,eAAe,CAAC,GAAGlE,QAAQ,CAACmC,SAAS,IAAI,WAAW,CAAC;EAC1E,MAAM,CAACgC,MAAM,EAAEC,SAAS,CAAC,GAAGpE,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACqE,kBAAkB,EAAEC,mBAAmB,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACuE,mBAAmB,EAAEC,oBAAoB,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACyE,YAAY,EAAEC,eAAe,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC2E,eAAe,EAAEC,kBAAkB,CAAC,GAAG5E,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACAC,SAAS,CAAC,MAAM;IACfuD,iBAAiB,CAACf,WAAW,IAAI,GAAG,CAAC;IACrCmB,kBAAkB,CAACrB,YAAY,IAAI,SAAS,CAAC;EAC9C,CAAC,EAAE,CAACE,WAAW,EAAEF,YAAY,CAAC,CAAC;EAE/B,MAAMsC,kBAAkB,GAAGA,CAAA,KAAM;IAChC,IAAIJ,YAAY,IAAIE,eAAe,EAAE;MACpC;IACD;IAEAvC,YAAY,CAAC6B,YAAY,CAAC;IAC1B3B,WAAW,CAACe,WAAW,IAAI,IAAI,CAAC;IAChCX,cAAc,CAACa,cAAc,IAAI,GAAG,CAAC;IACrCf,eAAe,CAACmB,eAAe,CAAC;IAChCzB,cAAc,CAAC2B,mBAAmB,CAAC;IACnCf,eAAe,CAACiB,gBAAgB,CAAC;IACjCnB,UAAU,CAACa,UAAU,CAAC;IAEtB,MAAMqB,YAAY,GAAGP,mBAAmB,GAAGlC,QAAQ,GAAGgB,WAAW;IACjE;IACArC,mBAAmB,CAClBiD,YAAY,EACZc,QAAQ,CAACD,YAAY,IAAI,IAAI,CAAC,EAC9BC,QAAQ,CAACxB,cAAc,IAAI,GAAG,CAAC,EAC/B,IAAI,EACJwB,QAAQ,CAAC1B,WAAW,IAAI,GAAG,CAC5B,CAAC;;IAED;IACA,MAAM2B,cAAmC,GAAG;MAC3CC,QAAQ,EAAEhB,YAAY;MACtBiB,eAAe,EAAEb,kBAAkB,GAAGR,mBAAmB,GAAGsB,SAAS;MACrEC,OAAO,EAAEN,YAAY,IAAI,IAAI;MAC7BO,WAAW,EAAE1B,eAAe;MAC5B2B,UAAU,EAAE/B,cAAc,IAAI,GAAG;MACjCgC,MAAM,EAAE9B,UAAU,IAAI,IAAI;MAC1BZ,YAAY,EAAEkB;IACf,CAAC;;IAED;IACAyB,MAAM,CAACC,IAAI,CAACT,cAAc,CAAC,CAACU,OAAO,CAAEC,GAAG,IAAK;MAC5C,IAAIX,cAAc,CAACW,GAAG,CAAC,KAAKR,SAAS,EAAE;QACtC,OAAOH,cAAc,CAACW,GAAG,CAAC;MAC3B;IACD,CAAC,CAAC;;IAEF;IACA5C,sBAAsB,CAACiC,cAAc,CAAC;;IAEtC;IACAY,WAAW,CAAC,CAAC;IACb3C,mBAAmB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAM4C,eAAe,GAAGA,CAAA,KAAM;IAC7BvC,cAAc,CAACjB,QAAQ,CAAC;IACxBmB,iBAAiB,CAACf,WAAW,CAAC;IAC9BiB,aAAa,CAACf,OAAO,CAAC;IACtBiB,kBAAkB,CAACrB,YAAY,CAAC;IAChCuB,sBAAsB,CAAC7B,WAAW,CAAC;IACnC+B,mBAAmB,CAACnB,YAAY,CAAC;IACjCqB,eAAe,CAAC/B,SAAS,IAAI,WAAW,CAAC;IACzCmC,mBAAmB,CAAC,KAAK,CAAC;IAC1BE,oBAAoB,CAAC,KAAK,CAAC;IAC3BE,eAAe,CAAC,KAAK,CAAC;IACtBE,kBAAkB,CAAC,KAAK,CAAC;EAC1B,CAAC;EAED,MAAMgB,WAAW,GAAGA,CAAA,KAAM;IACzBC,eAAe,CAAC,CAAC;IACjBzB,SAAS,CAAC,KAAK,CAAC;IAChBrD,qBAAqB,CAAC,KAAK,CAAC;EAC7B,CAAC;EAED,IAAI,CAACoD,MAAM,EAAE,OAAO,IAAI;EAExB,MAAM2B,WAAW,GAAIC,UAAmB,KAAM;IAC7CC,MAAM,EAAED,UAAU,GAAG,+BAA+B,GAAG,MAAM;IAC7DE,YAAY,EAAE,MAAM;IACpBjE,eAAe,EAAE+D,UAAU,GAAG,SAAS,GAAG,SAAS;IACnDG,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,SAAS;IAAE;IACnBC,SAAS,EAAEL,UAAU,GAAG,yBAAyB,GAAG,MAAM;IAC1DM,OAAO,EAAEN,UAAU,GAAG,GAAG,GAAG,KAAK;IACjC1E,OAAO,EAAE,KAAK;IACdiF,UAAU,EAAE;EACb,CAAC,CAAC;EAEF,oBACCzF,OAAA;IAAK0F,SAAS,EAAC,mBAAmB;IAAAC,QAAA,eACjC3F,OAAA;MAAK0F,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC7B3F,OAAA;QAAK0F,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBACnC3F,OAAA,CAACR,UAAU;UACV,cAAW,OAAO;UAClBoG,OAAO,EAAEb,WAAY;UAAAY,QAAA,eAErB3F,OAAA,CAACN,2BAA2B;YAAAmG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACbhG,OAAA;UAAK0F,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAErF,SAAS,CAAC,QAAQ,EAAE;YAAE2F,YAAY,EAAE;UAAS,CAAC;QAAC;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpFhG,OAAA,CAACR,UAAU;UACV0G,IAAI,EAAC,OAAO;UACZ,cAAW,OAAO;UAClBN,OAAO,EAAEb,WAAY;UAAAY,QAAA,eAErB3F,OAAA,CAACL,SAAS;YAAAkG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eAENhG,OAAA;QACCmG,KAAK,EAAE;UACNC,UAAU,EAAE,QAAQ;UACpBjF,eAAe,EAAE,yBAAyB;UAC1CiE,YAAY,EAAE,6BAA6B;UAC3CiB,MAAM,EAAE,MAAM;UACdC,MAAM,EAAE;QACT,CAAE;QAAAX,QAAA,gBAEF3F,OAAA;UAAOmG,KAAK,EAAE;YAAEI,UAAU,EAAE;UAAM,CAAE;UAAAZ,QAAA,EAAErF,SAAS,CAAC,UAAU,EAAE;YAAE2F,YAAY,EAAE;UAAW,CAAC;QAAC;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClGhG,OAAA;UAAKmG,KAAK,EAAE;YAAEK,OAAO,EAAE,MAAM;YAAEhG,OAAO,EAAE,KAAK;YAAEiG,YAAY,EAAE,QAAQ;YAAEC,GAAG,EAAE;UAAM,CAAE;UAAAf,QAAA,gBACnF3F,OAAA;YACCmG,KAAK,EAAElB,WAAW,CAAC7B,YAAY,KAAK,WAAW,CAAE;YACjDwC,OAAO,EAAEA,CAAA,KAAM;cACdvC,eAAe,CAAC,WAAW,CAAC;cAC5B;cACAM,oBAAoB,CAAC,IAAI,CAAC;YAC3B,CAAE;YAAAgC,QAAA,EAEDrF,SAAS,CAAC,WAAW,EAAE;cAAE2F,YAAY,EAAE;YAAY,CAAC;UAAC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eAEThG,OAAA;YACCmG,KAAK,EAAElB,WAAW,CAAC7B,YAAY,KAAK,WAAW,CAAE;YACjDwC,OAAO,EAAEA,CAAA,KAAM;cACdvC,eAAe,CAAC,WAAW,CAAC;cAC5B;cACAM,oBAAoB,CAAC,IAAI,CAAC;YAC3B,CAAE;YAAAgC,QAAA,EAEDrF,SAAS,CAAC,WAAW,EAAE;cAAE2F,YAAY,EAAE;YAAY,CAAC;UAAC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAENhG,OAAA;QAAK0F,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC9B3F,OAAA,CAACX,GAAG;UAACqG,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBACjC3F,OAAA,CAACV,UAAU;YAACoG,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAErF,SAAS,CAAC,SAAS,EAAE;cAAE2F,YAAY,EAAE;YAAU,CAAC;UAAC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC5GhG,OAAA,CAACT,SAAS;YACToH,OAAO,EAAC,UAAU;YAClBC,KAAK,EAAEpE,WAAW,IAAI,GAAI;YAC1BqE,SAAS;YACTX,IAAI,EAAC,OAAO;YACZR,SAAS,EAAC,qBAAqB;YAC/BoB,QAAQ,EAAGC,CAAC,IAAK;cAEhB,MAAMC,UAAU,GAAG9C,QAAQ,CAAC6C,CAAC,CAACE,MAAM,CAACL,KAAK,CAAC,IAAI,CAAC;;cAEhD;cACA,IAAII,UAAU,GAAG,CAAC,IAAIA,UAAU,GAAG,EAAE,EAAE;gBACtCnD,eAAe,CAAC,IAAI,CAAC;cACtB,CAAC,MAAM;gBACNA,eAAe,CAAC,KAAK,CAAC;cACvB;cACApB,cAAc,CAACuE,UAAU,CAACE,QAAQ,CAAC,CAAC,CAAC;cACrCvD,oBAAoB,CAAC,IAAI,CAAC;YAC3B,CAAE;YACFwD,UAAU,EAAE;cACXC,YAAY,EAAE,IAAI;cAClBC,EAAE,EAAE;gBACH,0CAA0C,EAAE;kBAAElC,MAAM,EAAE;gBAAO,CAAC;gBAC9D,gDAAgD,EAAE;kBAAEA,MAAM,EAAE;gBAAO,CAAC;gBACpE,YAAY,EAAE;kBAAEA,MAAM,EAAE;gBAAO;cAChC;YACD,CAAE;YACFmC,KAAK,EAAE1D;UAAa;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EACLpC,YAAY,iBACZ5D,OAAA,CAACV,UAAU;UACV6G,KAAK,EAAE;YACNoB,QAAQ,EAAE,MAAM;YAChBlC,KAAK,EAAE,SAAS;YAChBmC,SAAS,EAAE,MAAM;YACjBC,GAAG,EAAE,MAAM;YACXC,IAAI,EAAE,CAAC;YACPC,YAAY,EAAE,KAAK;YACnBnB,OAAO,EAAE;UACV,CAAE;UAAAb,QAAA,gBACF3F,OAAA;YAAMmG,KAAK,EAAE;cAAEK,OAAO,EAAE,MAAM;cAAEe,QAAQ,EAAE,MAAM;cAAEnB,UAAU,EAAE,QAAQ;cAAEwB,WAAW,EAAE;YAAM,CAAE;YAE7FC,uBAAuB,EAAE;cAAEC,MAAM,EAAEjI;YAAQ;UAAE;YAAAgG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,EACD1F,SAAS,CAAC,qBAAqB,EAAE;YAAE2F,YAAY,EAAE;UAAsC,CAAC,CAAC;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E,CACZ,eACDhG,OAAA,CAACX,GAAG;UAACqG,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBACjC3F,OAAA,CAACV,UAAU;YAACoG,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAErF,SAAS,CAAC,aAAa,EAAE;cAAE2F,YAAY,EAAE;YAAc,CAAC;UAAC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACpHhG,OAAA,CAACT,SAAS;YACToH,OAAO,EAAC,UAAU;YAClBC,KAAK,EAAElE,cAAc,IAAI,GAAI;YAC7BmE,SAAS;YACTX,IAAI,EAAC,OAAO;YACZR,SAAS,EAAC,qBAAqB;YAC/BoB,QAAQ,EAAGC,CAAC,IAAK;cAEhB,MAAMC,UAAU,GAAG9C,QAAQ,CAAC6C,CAAC,CAACE,MAAM,CAACL,KAAK,CAAC,IAAI,CAAC;;cAEhD;cACA,IAAII,UAAU,GAAG,CAAC,IAAIA,UAAU,GAAG,EAAE,EAAE;gBACtCjD,kBAAkB,CAAC,IAAI,CAAC;cACzB,CAAC,MAAM;gBACNA,kBAAkB,CAAC,KAAK,CAAC;cAC1B;cAEApB,iBAAiB,CAACqE,UAAU,CAACE,QAAQ,CAAC,CAAC,CAAC;YACzC,CAAE;YACFC,UAAU,EAAE;cACXC,YAAY,EAAE,IAAI;cAClBC,EAAE,EAAE;gBACH,0CAA0C,EAAE;kBAAElC,MAAM,EAAE;gBAAO,CAAC;gBAC9D,gDAAgD,EAAE;kBAAEA,MAAM,EAAE;gBAAO,CAAC;gBACpE,YAAY,EAAE;kBAAEA,MAAM,EAAE;gBAAO;cAChC;YACD,CAAE;YACFmC,KAAK,EAAExD;UAAgB;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EACLlC,eAAe,iBACf9D,OAAA,CAACV,UAAU;UACV6G,KAAK,EAAE;YACNoB,QAAQ,EAAE,MAAM;YAChBlC,KAAK,EAAE,SAAS;YAChBmC,SAAS,EAAE,MAAM;YACjBC,GAAG,EAAE,MAAM;YACXC,IAAI,EAAE,CAAC;YACPC,YAAY,EAAE,KAAK;YACnBnB,OAAO,EAAE;UACV,CAAE;UAAAb,QAAA,gBACF3F,OAAA;YAAMmG,KAAK,EAAE;cAAEK,OAAO,EAAE,MAAM;cAAEe,QAAQ,EAAE,MAAM;cAAEnB,UAAU,EAAE,QAAQ;cAAEwB,WAAW,EAAE;YAAM,CAAE;YAE7FC,uBAAuB,EAAE;cAAEC,MAAM,EAAEjI;YAAQ;UAAE;YAAAgG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,EACD1F,SAAS,CAAC,yBAAyB,EAAE;YAAE2F,YAAY,EAAE;UAAsC,CAAC,CAAC;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnF,CACZ,eAaDhG,OAAA,CAACX,GAAG;UAACqG,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBACjC3F,OAAA,CAACV,UAAU;YAACoG,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAErF,SAAS,CAAC,QAAQ,EAAE;cAAE2F,YAAY,EAAE;YAAS,CAAC;UAAC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC1GhG,OAAA;YACC+H,IAAI,EAAC,OAAO;YACZnB,KAAK,EAAE9D,eAAgB;YACvBgE,QAAQ,EAAGC,CAAC,IAAKhE,kBAAkB,CAACgE,CAAC,CAACE,MAAM,CAACL,KAAK,CAAE;YACpDlB,SAAS,EAAC;UAAmB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACNhG,OAAA,CAACX,GAAG;UAACqG,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBACjC3F,OAAA,CAACV,UAAU;YAACoG,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAErF,SAAS,CAAC,YAAY,EAAE;cAAE2F,YAAY,EAAE;YAAa,CAAC;UAAC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAClHhG,OAAA;YACC+H,IAAI,EAAC,OAAO;YACZnB,KAAK,EAAE5D,mBAAoB;YAC3B8D,QAAQ,EAAGC,CAAC,IAAK;cAChBtD,mBAAmB,CAAC,IAAI,CAAC;cACzBR,sBAAsB,CAAC8D,CAAC,CAACE,MAAM,CAACL,KAAK,CAAC;YACvC,CAAE;YACFlB,SAAS,EAAC;UAAmB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAUF,CAAC,eACNhG,OAAA;QAAK0F,SAAS,EAAC,oBAAoB;QAAAC,QAAA,eAClC3F,OAAA,CAACP,MAAM;UACNkH,OAAO,EAAC,WAAW;UACnBf,OAAO,EAAE5B,kBAAmB;UAC5B0B,SAAS,EAAE,aAAa9B,YAAY,IAAIE,eAAe,GAAG,UAAU,GAAG,EAAE,EAAG;UAC5EkE,QAAQ,EAAEpE,YAAY,IAAIE,eAAgB;UAAA6B,QAAA,EAEzCrF,SAAS,CAAC,OAAO,EAAE;YAAE2F,YAAY,EAAE;UAAQ,CAAC;QAAC;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAER,CAAC;AAAC5F,EAAA,CAhWIH,gBAAgB;EAAA,QACIH,cAAc,EAgCnCF,cAAc;AAAA;AAAAqI,EAAA,GAjCbhI,gBAAgB;AAkWtB,eAAeA,gBAAgB;AAAC,IAAAgI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}