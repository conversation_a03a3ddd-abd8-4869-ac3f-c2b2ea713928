{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideSetting\\\\PopupSections\\\\ImageGenerationPopup.tsx\",\n  _s = $RefreshSig$();\nimport React, { useContext, useState, useEffect } from 'react';\nimport { magicPen } from '../../../assets/icons/icons';\nimport { Box, IconButton } from '@mui/material';\nimport { GenerateImageWithUserPrompt, EnhanceUserPrompt } from '../../../services/AIService';\nimport { AccountContext } from '../../login/AccountContext';\nimport { useTranslation } from 'react-i18next';\nimport { useSnackbar } from '../guideList/SnackbarContext';\nimport useDrawerStore from '../../../store/drawerStore';\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ImageGenerationPopup = ({\n  openGenAiImagePopup,\n  setOpenGenAiImagePopup,\n  handleImageUploadFormApp,\n  setReplaceImage\n}) => {\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    openSnackbar\n  } = useSnackbar();\n  const {\n    imageAnchorEl\n  } = useDrawerStore(state => state);\n  const [isHovered, setIsHovered] = useState(false);\n  const [abortController, setAbortController] = useState(null);\n\n  // Function to get guidepopup position and dimensions\n  const getGuidePopupPosition = () => {\n    const element = document.querySelector('.qadpt-guide-popup .MuiDialog-paper') || document.getElementById('guide-popup');\n    if (element) {\n      const rect = element.getBoundingClientRect();\n      return {\n        top: rect.top,\n        left: rect.left,\n        width: rect.width,\n        height: rect.height\n      };\n    }\n    return null;\n  };\n\n  // Function to calculate adaptive positioning\n  const calculatePosition = () => {\n    const guidePopupPos = getGuidePopupPosition();\n    if (!guidePopupPos) {\n      return {\n        top: Math.max(20, (window.innerHeight - 500) / 2),\n        left: 0\n      };\n    }\n    const viewportWidth = window.innerWidth;\n    const viewportHeight = window.innerHeight;\n    const popupWidth = 260;\n    const popupHeight = 580;\n    const gap = 15;\n    const headerHeight = 56;\n    const bottomPadding = 20;\n    const topPadding = 20;\n\n    // Calculate optimal vertical position\n    const idealTop = (viewportHeight - popupHeight) / 2;\n    const minTop = headerHeight + topPadding;\n    const maxTop = viewportHeight - popupHeight - bottomPadding;\n    const viewportCenteredTop = Math.max(minTop, Math.min(idealTop, maxTop));\n    const requiredSpaceForExternal = guidePopupPos.left + guidePopupPos.width + gap + popupWidth;\n    const hasSpaceForExternal = requiredSpaceForExternal <= viewportWidth - 20;\n    if (hasSpaceForExternal) {\n      // External positioning: to the right of guidepopup with gap\n      const externalLeft = guidePopupPos.left + guidePopupPos.width + gap;\n      return {\n        top: viewportCenteredTop,\n        left: Math.min(externalLeft, viewportWidth - popupWidth - 10)\n      };\n    } else {\n      // Internal positioning: centered vertically in viewport\n      const maxInternalLeft = guidePopupPos.left + guidePopupPos.width - popupWidth - 15;\n      const minInternalLeft = guidePopupPos.left + 15;\n      const internalLeft = Math.max(minInternalLeft, maxInternalLeft);\n      return {\n        top: viewportCenteredTop,\n        left: Math.min(internalLeft, viewportWidth - popupWidth - 10)\n      };\n    }\n  };\n\n  // Initialize with calculated position or default\n  const [popupPosition, setPopupPosition] = useState(() => {\n    if (openGenAiImagePopup) {\n      return calculatePosition();\n    }\n    return {\n      top: Math.max(20, (window.innerHeight - 500) / 2),\n      left: -320\n    };\n  });\n  const [selectedStyle, setSelectedStyle] = useState('Professional');\n  const [selectedColor, setSelectedColor] = useState('Black & White');\n  const [selectedRatio, setSelectedRatio] = useState('16:9');\n  const {\n    accountId\n  } = useContext(AccountContext);\n  const styles = ['Professional', 'Formal', 'Friendly', 'Casual', 'Storytelling', 'Direct'];\n  const colors = ['Black & White', 'Cold Neon', 'Vibrant', 'Softhue', 'Gradient', 'Retro'];\n  const ratios = ['1:1', '16:9', '9:16'];\n  const [description, setDescription] = useState('');\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [isEnhancing, setIsEnhancing] = useState(false);\n\n  // Update positioning when popup opens or window resizes\n  useEffect(() => {\n    if (openGenAiImagePopup) {\n      const newPosition = calculatePosition();\n      setPopupPosition(newPosition);\n      const handleResize = () => {\n        const updatedPosition = calculatePosition();\n        setPopupPosition(updatedPosition);\n      };\n      window.addEventListener('resize', handleResize);\n      return () => window.removeEventListener('resize', handleResize);\n    }\n  }, [openGenAiImagePopup]);\n  const handleClose = () => {\n    setOpenGenAiImagePopup(false);\n  };\n  const onImageGenerated = imageUrl => {\n    // const timeStamp = () => new Date.now().toISOString(); // or Date.now()\n\n    let file = {\n      Url: imageUrl,\n      FileName: `Generated Image ${Date.now()}`,\n      IsAiGenerated: true\n    };\n    handleImageUploadFormApp(file);\n    setOpenGenAiImagePopup(false);\n  };\n  const handleEnhanceDescription = async () => {\n    if (description.trim() === \"\") {\n      openSnackbar(\"Please enter a description first.\", \"error\");\n      return;\n    }\n    setIsEnhancing(true);\n    try {\n      const enhancedPrompt = await EnhanceUserPrompt(description, accountId, openSnackbar);\n      if (enhancedPrompt) {\n        setDescription(enhancedPrompt);\n      }\n    } catch (error) {\n      console.error(\"Error enhancing description:\", error);\n    } finally {\n      setIsEnhancing(false);\n    }\n  };\n  const GenerateImage = () => {\n    if (description === \"\") return;\n\n    // Validate that we have the correct image context\n    // containerId is required, but buttonId can be empty for new image generation\n    if (!imageAnchorEl.containerId) {\n      openSnackbar(\"Error: Unable to identify which image container to use. Please try again.\", \"error\");\n      return;\n    }\n    setIsGenerating(true); // Start loader\n    const controller = new AbortController();\n    setAbortController(controller);\n    const userPromptWithSelectedOptions = `User Asked: ${description} ${selectedStyle !== \"\" ? \" and Image Should be in the Style \" + selectedStyle : \"\"} ${selectedColor !== \"\" ? \" and Image Should be in Color Palette \" + selectedColor : \"\"} ${selectedRatio !== \"\" ? \" and Image Should be in Aspect Ratio \" + selectedRatio : \"\"}`;\n    GenerateImageWithUserPrompt(userPromptWithSelectedOptions, accountId, imageUrl => {\n      onImageGenerated(imageUrl);\n      setIsGenerating(false); // Stop loader\n      setAbortController(null);\n    }, openSnackbar, controller.signal);\n  };\n  const blackMagicPen = magicPen.replace(/stroke=\"white\"/g, 'stroke=\"black\"');\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadpt-genai-popup\",\n      style: {\n        top: `${popupPosition.top}px`,\n        left: openGenAiImagePopup ? `${popupPosition.left}px` : '-320px',\n        transition: openGenAiImagePopup ? 'left 0.3s ease-in-out' : 'none' // Smooth transition when opening\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        className: \"qadpt-genai-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: translate(\"Generate Images\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"small\",\n          \"aria-label\": \"close\",\n          onClick: handleClose,\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-desccont\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-txt-cont\",\n            children: /*#__PURE__*/_jsxDEV(\"textarea\", {\n              placeholder: translate(\"Please describe your image...\"),\n              value: description,\n              onChange: e => {\n                const el = e.target;\n                el.style.height = 'auto';\n                el.style.height = Math.min(el.scrollHeight, 70) + 'px';\n                setDescription(e.target.value);\n              },\n              className: \"qadpt-txtarea\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-btncont\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleEnhanceDescription,\n              disabled: isEnhancing || description.trim() === \"\",\n              className: \"qadpt-enhbtn\",\n              children: [isEnhancing ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-enhancing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: blackMagicPen\n                },\n                style: {\n                  display: \"flex\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 19\n              }, this), isEnhancing ? translate(\"Enhancing...\") : translate(\"Enhance AI\")]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-img-style\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-hdr\",\n            children: translate(\"Style\")\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-options\",\n            children: styles.map(style => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setSelectedStyle(style),\n              className: `qadpt-btnoption ${selectedStyle === style ? 'selected' : ''}`,\n              disabled: isGenerating,\n              children: translate(`${style}`)\n            }, style, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-img-style\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-hdr\",\n            children: translate(\"Colors\")\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-options\",\n            children: colors.map(color => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setSelectedColor(color),\n              className: `qadpt-btnoption ${selectedColor === color ? 'selected' : ''}`,\n              disabled: isGenerating,\n              children: translate(`${color}`)\n            }, color, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-img-style\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-hdr\",\n            children: translate(\"Ratio\")\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-options\",\n            children: ratios.map(ratio => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setSelectedRatio(ratio),\n              className: `qadpt-btnoption ${selectedRatio === ratio ? 'selected' : ''}`,\n              disabled: isGenerating,\n              children: ratio\n            }, ratio, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => {\n          if (isGenerating) {\n            abortController === null || abortController === void 0 ? void 0 : abortController.abort();\n            setIsGenerating(false);\n            setAbortController(null);\n            openSnackbar(translate(\"Generation stopped.\"), \"success\");\n          } else if (description.trim() !== '') {\n            GenerateImage();\n          }\n        },\n        onMouseEnter: () => setIsHovered(true),\n        onMouseLeave: () => setIsHovered(false),\n        disabled: isGenerating && !abortController,\n        className: `qadpr-genbtn-cont ${isGenerating ? 'generating' : ''} ${description.trim() === '' ? 'disabled' : ''}`,\n        children: isGenerating ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-generating\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 15\n          }, this), isHovered ? translate('Stop Generation') : translate('Generating...')]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            dangerouslySetInnerHTML: {\n              __html: magicPen\n            },\n            style: {\n              display: \"flex\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 15\n          }, this), translate('Generate Image')]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this)\n  }, void 0, false);\n};\n_s(ImageGenerationPopup, \"CsXgxs3tjjU4p9dYuxn2Lu56Ims=\", false, function () {\n  return [useTranslation, useSnackbar, useDrawerStore];\n});\n_c = ImageGenerationPopup;\nexport default ImageGenerationPopup;\nvar _c;\n$RefreshReg$(_c, \"ImageGenerationPopup\");", "map": {"version": 3, "names": ["React", "useContext", "useState", "useEffect", "magicPen", "Box", "IconButton", "GenerateImageWithUserPrompt", "EnhanceUserPrompt", "AccountContext", "useTranslation", "useSnackbar", "useDrawerStore", "CloseIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ImageGenerationPopup", "openGenAiImagePopup", "setOpenGenAiImagePopup", "handleImageUploadFormApp", "setReplaceImage", "_s", "t", "translate", "openSnackbar", "imageAnchorEl", "state", "isHovered", "setIsHovered", "abortController", "setAbortController", "getGuidePopupPosition", "element", "document", "querySelector", "getElementById", "rect", "getBoundingClientRect", "top", "left", "width", "height", "calculatePosition", "guidePopupPos", "Math", "max", "window", "innerHeight", "viewportWidth", "innerWidth", "viewportHeight", "popup<PERSON><PERSON><PERSON>", "popupHeight", "gap", "headerHeight", "bottomPadding", "topPadding", "idealTop", "minTop", "maxTop", "viewportCenteredTop", "min", "requiredSpaceForExternal", "hasSpaceForExternal", "externalLeft", "maxInternalLeft", "minInternalLeft", "internalLeft", "popupPosition", "setPopupPosition", "selected<PERSON><PERSON><PERSON>", "setSelectedStyle", "selectedColor", "setSelectedColor", "selectedRatio", "setSelectedRatio", "accountId", "styles", "colors", "ratios", "description", "setDescription", "isGenerating", "setIsGenerating", "isEnhancing", "setIsEnhancing", "newPosition", "handleResize", "updatedPosition", "addEventListener", "removeEventListener", "handleClose", "onImageGenerated", "imageUrl", "file", "Url", "FileName", "Date", "now", "IsAiGenerated", "handleEnhanceDescription", "trim", "enhancedPrompt", "error", "console", "GenerateImage", "containerId", "controller", "AbortController", "userPromptWithSelectedOptions", "signal", "blackMagicPen", "replace", "children", "className", "style", "transition", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "onClick", "placeholder", "value", "onChange", "e", "el", "target", "scrollHeight", "disabled", "dangerouslySetInnerHTML", "__html", "display", "map", "color", "ratio", "abort", "onMouseEnter", "onMouseLeave", "_c", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/guideSetting/PopupSections/ImageGenerationPopup.tsx"], "sourcesContent": ["import React, { useContext, useState, useEffect } from 'react';\r\nimport { closeIcon, magicPen } from '../../../assets/icons/icons';\r\nimport { Box,Typography,IconButton } from '@mui/material';\r\nimport { GenerateImageWithUserPrompt, EnhanceUserPrompt } from '../../../services/AIService';\r\nimport { AccountContext } from '../../login/AccountContext';\r\nimport { FileUpload } from '../../../models/FileUpload';\r\nimport { timeStamp } from 'console';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { useSnackbar } from '../guideList/SnackbarContext';\r\nimport useDrawerStore from '../../../store/drawerStore';\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\n\r\nconst ImageGenerationPopup = ({openGenAiImagePopup,setOpenGenAiImagePopup,handleImageUploadFormApp,setReplaceImage}:any) => {\r\n  const { t: translate } = useTranslation();\r\n  const { openSnackbar } = useSnackbar();\r\n  const { imageAnchorEl } = useDrawerStore((state) => state);\r\n\r\n  const [isHovered, setIsHovered] = useState(false);\r\n  const [abortController, setAbortController] = useState<AbortController | null>(null);\r\n  \r\n  // Function to get guidepopup position and dimensions\r\n  const getGuidePopupPosition = () => {\r\n    const element = document.querySelector('.qadpt-guide-popup .MuiDialog-paper') ||\r\n                    document.getElementById('guide-popup');\r\n    if (element) {\r\n      const rect = element.getBoundingClientRect();\r\n      return {\r\n        top: rect.top,\r\n        left: rect.left,\r\n        width: rect.width,\r\n        height: rect.height\r\n      };\r\n    }\r\n    return null;\r\n  };\r\n\r\n  // Function to calculate adaptive positioning\r\n  const calculatePosition = () => {\r\n    const guidePopupPos = getGuidePopupPosition();\r\n    if (!guidePopupPos) {\r\n      return {\r\n        top: Math.max(20, (window.innerHeight - 500) / 2),\r\n        left: 0\r\n      };\r\n    }\r\n\r\n    const viewportWidth = window.innerWidth;\r\n    const viewportHeight = window.innerHeight;\r\n    const popupWidth = 260;\r\n    const popupHeight = 580;\r\n    const gap = 15;\r\n    const headerHeight = 56;\r\n    const bottomPadding = 20;\r\n    const topPadding = 20;\r\n\r\n    // Calculate optimal vertical position\r\n    const idealTop = (viewportHeight - popupHeight) / 2;\r\n    const minTop = headerHeight + topPadding;\r\n    const maxTop = viewportHeight - popupHeight - bottomPadding;\r\n    \r\n    const viewportCenteredTop = Math.max(minTop, Math.min(idealTop, maxTop));\r\n\r\n    const requiredSpaceForExternal = guidePopupPos.left + guidePopupPos.width + gap + popupWidth;\r\n    const hasSpaceForExternal = requiredSpaceForExternal <= viewportWidth - 20;\r\n\r\n    if (hasSpaceForExternal) {\r\n      // External positioning: to the right of guidepopup with gap\r\n      const externalLeft = guidePopupPos.left + guidePopupPos.width + gap;\r\n      return {\r\n        top: viewportCenteredTop,\r\n        left: Math.min(externalLeft, viewportWidth - popupWidth - 10),\r\n      };\r\n    } else {\r\n      // Internal positioning: centered vertically in viewport\r\n      const maxInternalLeft = guidePopupPos.left + guidePopupPos.width - popupWidth - 15;\r\n      const minInternalLeft = guidePopupPos.left + 15;\r\n      const internalLeft = Math.max(minInternalLeft, maxInternalLeft);\r\n      \r\n      return {\r\n        top: viewportCenteredTop,\r\n        left: Math.min(internalLeft, viewportWidth - popupWidth - 10), \r\n      };\r\n    }\r\n  };\r\n\r\n  // Initialize with calculated position or default\r\n  const [popupPosition, setPopupPosition] = useState(() => {\r\n    if (openGenAiImagePopup) {\r\n      return calculatePosition();\r\n    }\r\n    return {\r\n      top: Math.max(20, (window.innerHeight - 500) / 2),\r\n      left: -320 \r\n    };\r\n  });\r\n\r\n  const [selectedStyle, setSelectedStyle] = useState('Professional');\r\n  const [selectedColor, setSelectedColor] = useState('Black & White');\r\n  const [selectedRatio, setSelectedRatio] = useState('16:9');\r\n\r\n  const { accountId } = useContext(AccountContext);\r\n\r\n  const styles = ['Professional', 'Formal', 'Friendly', 'Casual', 'Storytelling', 'Direct'];\r\n  const colors = ['Black & White', 'Cold Neon', 'Vibrant', 'Softhue', 'Gradient', 'Retro'];\r\n  const ratios = ['1:1', '16:9','9:16'];\r\n  const [description, setDescription] = useState('');\r\n  const [isGenerating, setIsGenerating] = useState(false);\r\n  const [isEnhancing, setIsEnhancing] = useState(false);\r\n\r\n  // Update positioning when popup opens or window resizes\r\n  useEffect(() => {\r\n    if (openGenAiImagePopup) {\r\n      const newPosition = calculatePosition();\r\n      setPopupPosition(newPosition);\r\n\r\n      const handleResize = () => {\r\n        const updatedPosition = calculatePosition();\r\n        setPopupPosition(updatedPosition);\r\n      };\r\n\r\n      window.addEventListener('resize', handleResize);\r\n      return () => window.removeEventListener('resize', handleResize);\r\n    }\r\n  }, [openGenAiImagePopup]);\r\n\r\n  const handleClose = () => {\r\n    setOpenGenAiImagePopup(false);\r\n  };\r\n\r\n  const onImageGenerated = (imageUrl: any) => {\r\n    \r\n    // const timeStamp = () => new Date.now().toISOString(); // or Date.now()\r\n\r\n    let file: any = {\r\n      Url: imageUrl,\r\n      FileName: `Generated Image ${Date.now()}`,\r\n      IsAiGenerated: true,\r\n    };\r\n    \r\n    handleImageUploadFormApp(file);\r\n    setOpenGenAiImagePopup(false);\r\n  }\r\n\r\n  const handleEnhanceDescription = async () => {\r\n    if (description.trim() === \"\") {\r\n      openSnackbar(\"Please enter a description first.\", \"error\");\r\n      return;\r\n    }\r\n\r\n    setIsEnhancing(true);\r\n    try {\r\n      const enhancedPrompt = await EnhanceUserPrompt(description, accountId, openSnackbar);\r\n      if (enhancedPrompt) {\r\n        setDescription(enhancedPrompt);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error enhancing description:\", error);\r\n    } finally {\r\n      setIsEnhancing(false);\r\n    }\r\n  };\r\n\r\n  const GenerateImage = () => {\r\n    if (description === \"\") return;\r\n\r\n  // Validate that we have the correct image context\r\n  // containerId is required, but buttonId can be empty for new image generation\r\n    if (!imageAnchorEl.containerId) {\r\n      openSnackbar(\"Error: Unable to identify which image container to use. Please try again.\", \"error\");\r\n      return;\r\n    }\r\n\r\n  setIsGenerating(true); // Start loader\r\n    const controller = new AbortController();\r\n    setAbortController(controller);\r\n\r\n    const userPromptWithSelectedOptions = `User Asked: ${description} ${\r\n      selectedStyle !== \"\" ? \" and Image Should be in the Style \" + selectedStyle : \"\"\r\n    } ${\r\n      selectedColor !== \"\" ? \" and Image Should be in Color Palette \" + selectedColor : \"\"\r\n    } ${\r\n      selectedRatio !== \"\" ? \" and Image Should be in Aspect Ratio \" + selectedRatio : \"\"\r\n    }`;\r\n\r\n    GenerateImageWithUserPrompt(userPromptWithSelectedOptions, accountId, (imageUrl: any) => {\r\n      onImageGenerated(imageUrl);\r\n    setIsGenerating(false); // Stop loader\r\n      setAbortController(null);\r\n    }, openSnackbar, controller.signal);\r\n  };\r\n\r\n  const blackMagicPen = magicPen.replace(/stroke=\"white\"/g, 'stroke=\"black\"');\r\n  \r\n  return (\r\n    <>\r\n      <div className='qadpt-genai-popup'\r\n        style={{\r\n          top: `${popupPosition.top}px`,\r\n          left: openGenAiImagePopup ? `${popupPosition.left}px` : '-320px',\r\n          transition: openGenAiImagePopup ? 'left 0.3s ease-in-out' : 'none' // Smooth transition when opening\r\n        }}\r\n      >\r\n        <Box className=\"qadpt-genai-header\">\r\n          <div>{translate(\"Generate Images\")}</div>\r\n          <IconButton size=\"small\" aria-label=\"close\" onClick={handleClose}>\r\n            <CloseIcon />\r\n          </IconButton>\r\n        </Box>\r\n        \r\n        <div>\r\n          <div className=\"qadpt-desccont\">\r\n            <div className=\"qadpt-txt-cont\">\r\n              <textarea\r\n              \r\n                placeholder={translate(\"Please describe your image...\")}\r\n                value={description}\r\n onChange={(e) => {\r\n    const el = e.target;\r\n    el.style.height = 'auto'; \r\n    el.style.height = Math.min(el.scrollHeight, 70) + 'px';\r\n    setDescription(e.target.value);\r\n                }}\r\n                className=\"qadpt-txtarea\"\r\n              />\r\n            </div>\r\n\r\n            <div className=\"qadpt-btncont\">\r\n              <button\r\n                onClick={handleEnhanceDescription}\r\n                disabled={isEnhancing || description.trim() === \"\"}\r\n                className=\"qadpt-enhbtn\"\r\n              >\r\n                {isEnhancing ? (\r\n                  <div className=\"qadpt-enhancing\" />\r\n                ) : (\r\n                  <span dangerouslySetInnerHTML={{ __html: blackMagicPen }} style={{ display: \"flex\" }} />\r\n                )}\r\n                {isEnhancing ? translate(\"Enhancing...\") : translate(\"Enhance AI\")}\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"qadpt-img-style\">\r\n            <div className=\"qadpt-hdr\">{translate(\"Style\")}</div>\r\n            <div className=\"qadpt-options\">\r\n              {styles.map((style) => (\r\n                <button\r\n                  key={style}\r\n                  onClick={() => setSelectedStyle(style)}\r\n                  className={`qadpt-btnoption ${selectedStyle === style ? 'selected' : ''}`}\r\n                  disabled={isGenerating}\r\n                >\r\n                  {translate(`${style}`)}\r\n                </button>\r\n              ))}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"qadpt-img-style\">\r\n            <div className=\"qadpt-hdr\">{translate(\"Colors\")}</div>\r\n            <div className=\"qadpt-options\">\r\n              {colors.map((color) => (\r\n                <button\r\n                  key={color}\r\n                  onClick={() => setSelectedColor(color)}\r\n                  className={`qadpt-btnoption ${selectedColor === color ? 'selected' : ''}`}\r\n                  disabled={isGenerating}\r\n                >\r\n                   {translate(`${color}`)}\r\n                </button>\r\n              ))}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"qadpt-img-style\">\r\n            <div className=\"qadpt-hdr\">{translate(\"Ratio\")}</div>\r\n            <div className=\"qadpt-options\">\r\n              {ratios.map((ratio) => (\r\n                <button\r\n                  key={ratio}\r\n                  onClick={() => setSelectedRatio(ratio)}\r\n                  className={`qadpt-btnoption ${selectedRatio === ratio ? 'selected' : ''}`}\r\n                  disabled={isGenerating}\r\n                >\r\n                  {ratio}\r\n                </button>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <button\r\n          onClick={() => {\r\n            if (isGenerating) {\r\n              abortController?.abort();\r\n              setIsGenerating(false);\r\n              setAbortController(null);\r\n              openSnackbar(translate(\"Generation stopped.\"), \"success\");\r\n            } else if (description.trim() !== '') {\r\n              GenerateImage();\r\n            }\r\n          }}\r\n          onMouseEnter={() => setIsHovered(true)}\r\n          onMouseLeave={() => setIsHovered(false)}\r\n          disabled={isGenerating && !abortController}\r\n          className={`qadpr-genbtn-cont ${isGenerating ? 'generating' : ''} ${\r\n            description.trim() === '' ? 'disabled' : ''\r\n          }`}\r\n        >\r\n          {isGenerating ? (\r\n            <>\r\n              <div className=\"qadpt-generating\" />\r\n              {isHovered ? translate('Stop Generation') : translate('Generating...')}\r\n            </>\r\n          ) : (\r\n            <>\r\n              <span dangerouslySetInnerHTML={{ __html: magicPen }} style={{ display: \"flex\" }} />\r\n              {translate('Generate Image')}\r\n            </>\r\n          )}\r\n        </button>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ImageGenerationPopup;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC9D,SAAoBC,QAAQ,QAAQ,6BAA6B;AACjE,SAASC,GAAG,EAAYC,UAAU,QAAQ,eAAe;AACzD,SAASC,2BAA2B,EAAEC,iBAAiB,QAAQ,6BAA6B;AAC5F,SAASC,cAAc,QAAQ,4BAA4B;AAG3D,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAOC,SAAS,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElD,MAAMC,oBAAoB,GAAGA,CAAC;EAACC,mBAAmB;EAACC,sBAAsB;EAACC,wBAAwB;EAACC;AAAmB,CAAC,KAAK;EAAAC,EAAA;EAC1H,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGf,cAAc,CAAC,CAAC;EACzC,MAAM;IAAEgB;EAAa,CAAC,GAAGf,WAAW,CAAC,CAAC;EACtC,MAAM;IAAEgB;EAAc,CAAC,GAAGf,cAAc,CAAEgB,KAAK,IAAKA,KAAK,CAAC;EAE1D,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC6B,eAAe,EAAEC,kBAAkB,CAAC,GAAG9B,QAAQ,CAAyB,IAAI,CAAC;;EAEpF;EACA,MAAM+B,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAMC,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,qCAAqC,CAAC,IAC7DD,QAAQ,CAACE,cAAc,CAAC,aAAa,CAAC;IACtD,IAAIH,OAAO,EAAE;MACX,MAAMI,IAAI,GAAGJ,OAAO,CAACK,qBAAqB,CAAC,CAAC;MAC5C,OAAO;QACLC,GAAG,EAAEF,IAAI,CAACE,GAAG;QACbC,IAAI,EAAEH,IAAI,CAACG,IAAI;QACfC,KAAK,EAAEJ,IAAI,CAACI,KAAK;QACjBC,MAAM,EAAEL,IAAI,CAACK;MACf,CAAC;IACH;IACA,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,aAAa,GAAGZ,qBAAqB,CAAC,CAAC;IAC7C,IAAI,CAACY,aAAa,EAAE;MAClB,OAAO;QACLL,GAAG,EAAEM,IAAI,CAACC,GAAG,CAAC,EAAE,EAAE,CAACC,MAAM,CAACC,WAAW,GAAG,GAAG,IAAI,CAAC,CAAC;QACjDR,IAAI,EAAE;MACR,CAAC;IACH;IAEA,MAAMS,aAAa,GAAGF,MAAM,CAACG,UAAU;IACvC,MAAMC,cAAc,GAAGJ,MAAM,CAACC,WAAW;IACzC,MAAMI,UAAU,GAAG,GAAG;IACtB,MAAMC,WAAW,GAAG,GAAG;IACvB,MAAMC,GAAG,GAAG,EAAE;IACd,MAAMC,YAAY,GAAG,EAAE;IACvB,MAAMC,aAAa,GAAG,EAAE;IACxB,MAAMC,UAAU,GAAG,EAAE;;IAErB;IACA,MAAMC,QAAQ,GAAG,CAACP,cAAc,GAAGE,WAAW,IAAI,CAAC;IACnD,MAAMM,MAAM,GAAGJ,YAAY,GAAGE,UAAU;IACxC,MAAMG,MAAM,GAAGT,cAAc,GAAGE,WAAW,GAAGG,aAAa;IAE3D,MAAMK,mBAAmB,GAAGhB,IAAI,CAACC,GAAG,CAACa,MAAM,EAAEd,IAAI,CAACiB,GAAG,CAACJ,QAAQ,EAAEE,MAAM,CAAC,CAAC;IAExE,MAAMG,wBAAwB,GAAGnB,aAAa,CAACJ,IAAI,GAAGI,aAAa,CAACH,KAAK,GAAGa,GAAG,GAAGF,UAAU;IAC5F,MAAMY,mBAAmB,GAAGD,wBAAwB,IAAId,aAAa,GAAG,EAAE;IAE1E,IAAIe,mBAAmB,EAAE;MACvB;MACA,MAAMC,YAAY,GAAGrB,aAAa,CAACJ,IAAI,GAAGI,aAAa,CAACH,KAAK,GAAGa,GAAG;MACnE,OAAO;QACLf,GAAG,EAAEsB,mBAAmB;QACxBrB,IAAI,EAAEK,IAAI,CAACiB,GAAG,CAACG,YAAY,EAAEhB,aAAa,GAAGG,UAAU,GAAG,EAAE;MAC9D,CAAC;IACH,CAAC,MAAM;MACL;MACA,MAAMc,eAAe,GAAGtB,aAAa,CAACJ,IAAI,GAAGI,aAAa,CAACH,KAAK,GAAGW,UAAU,GAAG,EAAE;MAClF,MAAMe,eAAe,GAAGvB,aAAa,CAACJ,IAAI,GAAG,EAAE;MAC/C,MAAM4B,YAAY,GAAGvB,IAAI,CAACC,GAAG,CAACqB,eAAe,EAAED,eAAe,CAAC;MAE/D,OAAO;QACL3B,GAAG,EAAEsB,mBAAmB;QACxBrB,IAAI,EAAEK,IAAI,CAACiB,GAAG,CAACM,YAAY,EAAEnB,aAAa,GAAGG,UAAU,GAAG,EAAE;MAC9D,CAAC;IACH;EACF,CAAC;;EAED;EACA,MAAM,CAACiB,aAAa,EAAEC,gBAAgB,CAAC,GAAGrE,QAAQ,CAAC,MAAM;IACvD,IAAIiB,mBAAmB,EAAE;MACvB,OAAOyB,iBAAiB,CAAC,CAAC;IAC5B;IACA,OAAO;MACLJ,GAAG,EAAEM,IAAI,CAACC,GAAG,CAAC,EAAE,EAAE,CAACC,MAAM,CAACC,WAAW,GAAG,GAAG,IAAI,CAAC,CAAC;MACjDR,IAAI,EAAE,CAAC;IACT,CAAC;EACH,CAAC,CAAC;EAEF,MAAM,CAAC+B,aAAa,EAAEC,gBAAgB,CAAC,GAAGvE,QAAQ,CAAC,cAAc,CAAC;EAClE,MAAM,CAACwE,aAAa,EAAEC,gBAAgB,CAAC,GAAGzE,QAAQ,CAAC,eAAe,CAAC;EACnE,MAAM,CAAC0E,aAAa,EAAEC,gBAAgB,CAAC,GAAG3E,QAAQ,CAAC,MAAM,CAAC;EAE1D,MAAM;IAAE4E;EAAU,CAAC,GAAG7E,UAAU,CAACQ,cAAc,CAAC;EAEhD,MAAMsE,MAAM,GAAG,CAAC,cAAc,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,cAAc,EAAE,QAAQ,CAAC;EACzF,MAAMC,MAAM,GAAG,CAAC,eAAe,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,CAAC;EACxF,MAAMC,MAAM,GAAG,CAAC,KAAK,EAAE,MAAM,EAAC,MAAM,CAAC;EACrC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGjF,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACkF,YAAY,EAAEC,eAAe,CAAC,GAAGnF,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoF,WAAW,EAAEC,cAAc,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACAC,SAAS,CAAC,MAAM;IACd,IAAIgB,mBAAmB,EAAE;MACvB,MAAMqE,WAAW,GAAG5C,iBAAiB,CAAC,CAAC;MACvC2B,gBAAgB,CAACiB,WAAW,CAAC;MAE7B,MAAMC,YAAY,GAAGA,CAAA,KAAM;QACzB,MAAMC,eAAe,GAAG9C,iBAAiB,CAAC,CAAC;QAC3C2B,gBAAgB,CAACmB,eAAe,CAAC;MACnC,CAAC;MAED1C,MAAM,CAAC2C,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;MAC/C,OAAO,MAAMzC,MAAM,CAAC4C,mBAAmB,CAAC,QAAQ,EAAEH,YAAY,CAAC;IACjE;EACF,CAAC,EAAE,CAACtE,mBAAmB,CAAC,CAAC;EAEzB,MAAM0E,WAAW,GAAGA,CAAA,KAAM;IACxBzE,sBAAsB,CAAC,KAAK,CAAC;EAC/B,CAAC;EAED,MAAM0E,gBAAgB,GAAIC,QAAa,IAAK;IAE1C;;IAEA,IAAIC,IAAS,GAAG;MACdC,GAAG,EAAEF,QAAQ;MACbG,QAAQ,EAAE,mBAAmBC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MACzCC,aAAa,EAAE;IACjB,CAAC;IAEDhF,wBAAwB,CAAC2E,IAAI,CAAC;IAC9B5E,sBAAsB,CAAC,KAAK,CAAC;EAC/B,CAAC;EAED,MAAMkF,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAIpB,WAAW,CAACqB,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC7B7E,YAAY,CAAC,mCAAmC,EAAE,OAAO,CAAC;MAC1D;IACF;IAEA6D,cAAc,CAAC,IAAI,CAAC;IACpB,IAAI;MACF,MAAMiB,cAAc,GAAG,MAAMhG,iBAAiB,CAAC0E,WAAW,EAAEJ,SAAS,EAAEpD,YAAY,CAAC;MACpF,IAAI8E,cAAc,EAAE;QAClBrB,cAAc,CAACqB,cAAc,CAAC;MAChC;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD,CAAC,SAAS;MACRlB,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAMoB,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIzB,WAAW,KAAK,EAAE,EAAE;;IAE1B;IACA;IACE,IAAI,CAACvD,aAAa,CAACiF,WAAW,EAAE;MAC9BlF,YAAY,CAAC,2EAA2E,EAAE,OAAO,CAAC;MAClG;IACF;IAEF2D,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;IACrB,MAAMwB,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;IACxC9E,kBAAkB,CAAC6E,UAAU,CAAC;IAE9B,MAAME,6BAA6B,GAAG,eAAe7B,WAAW,IAC9DV,aAAa,KAAK,EAAE,GAAG,oCAAoC,GAAGA,aAAa,GAAG,EAAE,IAEhFE,aAAa,KAAK,EAAE,GAAG,wCAAwC,GAAGA,aAAa,GAAG,EAAE,IAEpFE,aAAa,KAAK,EAAE,GAAG,uCAAuC,GAAGA,aAAa,GAAG,EAAE,EACnF;IAEFrE,2BAA2B,CAACwG,6BAA6B,EAAEjC,SAAS,EAAGiB,QAAa,IAAK;MACvFD,gBAAgB,CAACC,QAAQ,CAAC;MAC5BV,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;MACtBrD,kBAAkB,CAAC,IAAI,CAAC;IAC1B,CAAC,EAAEN,YAAY,EAAEmF,UAAU,CAACG,MAAM,CAAC;EACrC,CAAC;EAED,MAAMC,aAAa,GAAG7G,QAAQ,CAAC8G,OAAO,CAAC,iBAAiB,EAAE,gBAAgB,CAAC;EAE3E,oBACEnG,OAAA,CAAAE,SAAA;IAAAkG,QAAA,eACEpG,OAAA;MAAKqG,SAAS,EAAC,mBAAmB;MAChCC,KAAK,EAAE;QACL7E,GAAG,EAAE,GAAG8B,aAAa,CAAC9B,GAAG,IAAI;QAC7BC,IAAI,EAAEtB,mBAAmB,GAAG,GAAGmD,aAAa,CAAC7B,IAAI,IAAI,GAAG,QAAQ;QAChE6E,UAAU,EAAEnG,mBAAmB,GAAG,uBAAuB,GAAG,MAAM,CAAC;MACrE,CAAE;MAAAgG,QAAA,gBAEFpG,OAAA,CAACV,GAAG;QAAC+G,SAAS,EAAC,oBAAoB;QAAAD,QAAA,gBACjCpG,OAAA;UAAAoG,QAAA,EAAM1F,SAAS,CAAC,iBAAiB;QAAC;UAAA8F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACzC3G,OAAA,CAACT,UAAU;UAACqH,IAAI,EAAC,OAAO;UAAC,cAAW,OAAO;UAACC,OAAO,EAAE/B,WAAY;UAAAsB,QAAA,eAC/DpG,OAAA,CAACF,SAAS;YAAA0G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAEN3G,OAAA;QAAAoG,QAAA,gBACEpG,OAAA;UAAKqG,SAAS,EAAC,gBAAgB;UAAAD,QAAA,gBAC7BpG,OAAA;YAAKqG,SAAS,EAAC,gBAAgB;YAAAD,QAAA,eAC7BpG,OAAA;cAEE8G,WAAW,EAAEpG,SAAS,CAAC,+BAA+B,CAAE;cACxDqG,KAAK,EAAE5C,WAAY;cAClC6C,QAAQ,EAAGC,CAAC,IAAK;gBACd,MAAMC,EAAE,GAAGD,CAAC,CAACE,MAAM;gBACnBD,EAAE,CAACZ,KAAK,CAAC1E,MAAM,GAAG,MAAM;gBACxBsF,EAAE,CAACZ,KAAK,CAAC1E,MAAM,GAAGG,IAAI,CAACiB,GAAG,CAACkE,EAAE,CAACE,YAAY,EAAE,EAAE,CAAC,GAAG,IAAI;gBACtDhD,cAAc,CAAC6C,CAAC,CAACE,MAAM,CAACJ,KAAK,CAAC;cAClB,CAAE;cACFV,SAAS,EAAC;YAAe;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN3G,OAAA;YAAKqG,SAAS,EAAC,eAAe;YAAAD,QAAA,eAC5BpG,OAAA;cACE6G,OAAO,EAAEtB,wBAAyB;cAClC8B,QAAQ,EAAE9C,WAAW,IAAIJ,WAAW,CAACqB,IAAI,CAAC,CAAC,KAAK,EAAG;cACnDa,SAAS,EAAC,cAAc;cAAAD,QAAA,GAEvB7B,WAAW,gBACVvE,OAAA;gBAAKqG,SAAS,EAAC;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEnC3G,OAAA;gBAAMsH,uBAAuB,EAAE;kBAAEC,MAAM,EAAErB;gBAAc,CAAE;gBAACI,KAAK,EAAE;kBAAEkB,OAAO,EAAE;gBAAO;cAAE;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CACxF,EACApC,WAAW,GAAG7D,SAAS,CAAC,cAAc,CAAC,GAAGA,SAAS,CAAC,YAAY,CAAC;YAAA;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN3G,OAAA;UAAKqG,SAAS,EAAC,iBAAiB;UAAAD,QAAA,gBAC9BpG,OAAA;YAAKqG,SAAS,EAAC,WAAW;YAAAD,QAAA,EAAE1F,SAAS,CAAC,OAAO;UAAC;YAAA8F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrD3G,OAAA;YAAKqG,SAAS,EAAC,eAAe;YAAAD,QAAA,EAC3BpC,MAAM,CAACyD,GAAG,CAAEnB,KAAK,iBAChBtG,OAAA;cAEE6G,OAAO,EAAEA,CAAA,KAAMnD,gBAAgB,CAAC4C,KAAK,CAAE;cACvCD,SAAS,EAAE,mBAAmB5C,aAAa,KAAK6C,KAAK,GAAG,UAAU,GAAG,EAAE,EAAG;cAC1Ee,QAAQ,EAAEhD,YAAa;cAAA+B,QAAA,EAEtB1F,SAAS,CAAC,GAAG4F,KAAK,EAAE;YAAC,GALjBA,KAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAMJ,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN3G,OAAA;UAAKqG,SAAS,EAAC,iBAAiB;UAAAD,QAAA,gBAC9BpG,OAAA;YAAKqG,SAAS,EAAC,WAAW;YAAAD,QAAA,EAAE1F,SAAS,CAAC,QAAQ;UAAC;YAAA8F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACtD3G,OAAA;YAAKqG,SAAS,EAAC,eAAe;YAAAD,QAAA,EAC3BnC,MAAM,CAACwD,GAAG,CAAEC,KAAK,iBAChB1H,OAAA;cAEE6G,OAAO,EAAEA,CAAA,KAAMjD,gBAAgB,CAAC8D,KAAK,CAAE;cACvCrB,SAAS,EAAE,mBAAmB1C,aAAa,KAAK+D,KAAK,GAAG,UAAU,GAAG,EAAE,EAAG;cAC1EL,QAAQ,EAAEhD,YAAa;cAAA+B,QAAA,EAErB1F,SAAS,CAAC,GAAGgH,KAAK,EAAE;YAAC,GALlBA,KAAK;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAMJ,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN3G,OAAA;UAAKqG,SAAS,EAAC,iBAAiB;UAAAD,QAAA,gBAC9BpG,OAAA;YAAKqG,SAAS,EAAC,WAAW;YAAAD,QAAA,EAAE1F,SAAS,CAAC,OAAO;UAAC;YAAA8F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrD3G,OAAA;YAAKqG,SAAS,EAAC,eAAe;YAAAD,QAAA,EAC3BlC,MAAM,CAACuD,GAAG,CAAEE,KAAK,iBAChB3H,OAAA;cAEE6G,OAAO,EAAEA,CAAA,KAAM/C,gBAAgB,CAAC6D,KAAK,CAAE;cACvCtB,SAAS,EAAE,mBAAmBxC,aAAa,KAAK8D,KAAK,GAAG,UAAU,GAAG,EAAE,EAAG;cAC1EN,QAAQ,EAAEhD,YAAa;cAAA+B,QAAA,EAEtBuB;YAAK,GALDA,KAAK;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAMJ,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN3G,OAAA;QACE6G,OAAO,EAAEA,CAAA,KAAM;UACb,IAAIxC,YAAY,EAAE;YAChBrD,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE4G,KAAK,CAAC,CAAC;YACxBtD,eAAe,CAAC,KAAK,CAAC;YACtBrD,kBAAkB,CAAC,IAAI,CAAC;YACxBN,YAAY,CAACD,SAAS,CAAC,qBAAqB,CAAC,EAAE,SAAS,CAAC;UAC3D,CAAC,MAAM,IAAIyD,WAAW,CAACqB,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;YACpCI,aAAa,CAAC,CAAC;UACjB;QACF,CAAE;QACFiC,YAAY,EAAEA,CAAA,KAAM9G,YAAY,CAAC,IAAI,CAAE;QACvC+G,YAAY,EAAEA,CAAA,KAAM/G,YAAY,CAAC,KAAK,CAAE;QACxCsG,QAAQ,EAAEhD,YAAY,IAAI,CAACrD,eAAgB;QAC3CqF,SAAS,EAAE,qBAAqBhC,YAAY,GAAG,YAAY,GAAG,EAAE,IAC9DF,WAAW,CAACqB,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,UAAU,GAAG,EAAE,EAC1C;QAAAY,QAAA,EAEF/B,YAAY,gBACXrE,OAAA,CAAAE,SAAA;UAAAkG,QAAA,gBACEpG,OAAA;YAAKqG,SAAS,EAAC;UAAkB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACnC7F,SAAS,GAAGJ,SAAS,CAAC,iBAAiB,CAAC,GAAGA,SAAS,CAAC,eAAe,CAAC;QAAA,eACtE,CAAC,gBAEHV,OAAA,CAAAE,SAAA;UAAAkG,QAAA,gBACEpG,OAAA;YAAMsH,uBAAuB,EAAE;cAAEC,MAAM,EAAElI;YAAS,CAAE;YAACiH,KAAK,EAAE;cAAEkB,OAAO,EAAE;YAAO;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAClFjG,SAAS,CAAC,gBAAgB,CAAC;QAAA,eAC5B;MACH;QAAA8F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC,gBACN,CAAC;AAEP,CAAC;AAACnG,EAAA,CAxTIL,oBAAoB;EAAA,QACCR,cAAc,EACdC,WAAW,EACVC,cAAc;AAAA;AAAAkI,EAAA,GAHpC5H,oBAAoB;AA0T1B,eAAeA,oBAAoB;AAAC,IAAA4H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}