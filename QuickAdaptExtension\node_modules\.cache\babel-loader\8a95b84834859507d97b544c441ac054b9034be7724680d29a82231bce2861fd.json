{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\checklist\\\\TitleSubTitle.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport { Box, Typography, TextField, IconButton, Button } from \"@mui/material\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport useDrawerStore from \"../../store/drawerStore\";\nimport { warning } from \"../../assets/icons/icons\";\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TitleSubTitle = ({\n  currentGuide,\n  onBack,\n  onClose\n}) => {\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    titlePopup,\n    setTitlePopup,\n    setDesignPopup,\n    titleColor,\n    setTitleColor,\n    checklistTitle,\n    setChecklistTitle,\n    checklistSubTitle,\n    setChecklistSubTitle,\n    checklistGuideMetaData,\n    updateChecklistTitleSubTitle,\n    setIsUnSavedChanges,\n    isUnSavedChanges\n  } = useDrawerStore(state => state);\n  const [titleError, setTitleError] = useState(\"\"); // Store error message\n  const [subtitleError, setsubTitleError] = useState(\"\"); // Store error message\n  const [isDisabled, setIsDisabled] = useState(true);\n  const [hasChanges, setHasChanges] = useState(false); // Track if any changes were made\n\n  function getLocalizedDefaults(t, existing = {}) {\n    var _existing$titleBold, _existing$titleItalic, _existing$subTitleBol, _existing$subTitleIta;\n    const defaultTitle = \"Checklist Title\";\n    const defaultSubTitle = \"Context about the tasks in the checklist below users should prioritize completing.\";\n    return {\n      ...existing,\n      title: (existing === null || existing === void 0 ? void 0 : existing.title) === defaultTitle || !(existing !== null && existing !== void 0 && existing.title) ? t(defaultTitle, {\n        defaultValue: defaultTitle\n      }) : existing === null || existing === void 0 ? void 0 : existing.title,\n      subTitle: (existing === null || existing === void 0 ? void 0 : existing.subTitle) === defaultSubTitle || !(existing !== null && existing !== void 0 && existing.subTitle) ? t(defaultSubTitle, {\n        defaultValue: defaultSubTitle\n      }) : existing === null || existing === void 0 ? void 0 : existing.subTitle,\n      titleColor: (existing === null || existing === void 0 ? void 0 : existing.titleColor) || \"#333\",\n      titleBold: (_existing$titleBold = existing === null || existing === void 0 ? void 0 : existing.titleBold) !== null && _existing$titleBold !== void 0 ? _existing$titleBold : true,\n      titleItalic: (_existing$titleItalic = existing === null || existing === void 0 ? void 0 : existing.titleItalic) !== null && _existing$titleItalic !== void 0 ? _existing$titleItalic : false,\n      subTitleColor: (existing === null || existing === void 0 ? void 0 : existing.subTitleColor) || \"#8D8D8D\",\n      subTitleBold: (_existing$subTitleBol = existing === null || existing === void 0 ? void 0 : existing.subTitleBold) !== null && _existing$subTitleBol !== void 0 ? _existing$subTitleBol : false,\n      subTitleItalic: (_existing$subTitleIta = existing === null || existing === void 0 ? void 0 : existing.subTitleItalic) !== null && _existing$subTitleIta !== void 0 ? _existing$subTitleIta : false\n    };\n  }\n\n  // Function to check if the Apply button should be enabled\n  const updateApplyButtonState = (titleErr, subtitleErr, changed) => {\n    // Enable the button if there are no errors AND changes have been made\n    setIsDisabled(!!titleErr || !!subtitleErr || !changed);\n  };\n  const handleTitleChange = e => {\n    const value = e.target.value;\n    let errorMessage = \"\";\n    if (value.length < 5) {\n      errorMessage = translate(\"Min: 5 Characters\");\n    } else if (value.length > 50) {\n      errorMessage = translate(\"Max: 50 Characters\");\n    }\n    setTitleError(errorMessage);\n    setHasChanges(true); // Mark that changes have been made\n    updateApplyButtonState(errorMessage, subtitleError, true);\n    onPropertyChange(\"title\", value);\n  };\n  const handleSubTitleChange = e => {\n    const value = e.target.value;\n    let errorMessage = \"\";\n    if (value.length < 5) {\n      errorMessage = translate(\"Min: 5 Characters\");\n    } else if (value.length > 500) {\n      errorMessage = translate(\"Max: 500 Characters\");\n    }\n    setsubTitleError(errorMessage);\n    setHasChanges(true); // Mark that changes have been made\n    updateApplyButtonState(titleError, errorMessage, true);\n    onPropertyChange(\"subTitle\", value);\n  };\n  const [titleSubTitleProperties, setTitleSubTitleProperties] = useState(() => {\n    var _checklistGuideMetaDa;\n    return getLocalizedDefaults(translate, (_checklistGuideMetaDa = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa === void 0 ? void 0 : _checklistGuideMetaDa.TitleSubTitle);\n  });\n  const [tempTitle, setTempTitle] = useState(titleSubTitleProperties === null || titleSubTitleProperties === void 0 ? void 0 : titleSubTitleProperties.title);\n  const [tempSubTitle, settempTempTitle] = useState(titleSubTitleProperties === null || titleSubTitleProperties === void 0 ? void 0 : titleSubTitleProperties.subTitle);\n\n  // Store the initial state to compare for changes\n  const [initialState, setInitialState] = useState(titleSubTitleProperties);\n\n  // Effect to check for any changes compared to initial state\n  useEffect(() => {\n    // Compare current properties with initial state\n    const hasAnyChanges = Object.keys(titleSubTitleProperties).some(key => titleSubTitleProperties[key] !== initialState[key]);\n    setHasChanges(hasAnyChanges);\n    updateApplyButtonState(titleError, subtitleError, hasAnyChanges);\n  }, [titleSubTitleProperties, initialState, titleError, subtitleError]);\n  const handleTitleColorChange = e => setTitleColor(e.target.value);\n  const onPropertyChange = (key, value) => {\n    setTitleSubTitleProperties(prevState => {\n      // Check if the value has actually changed\n      if (prevState[key] !== value) {\n        setHasChanges(true);\n        updateApplyButtonState(titleError, subtitleError, true);\n      }\n      return {\n        ...prevState,\n        [key]: value\n      };\n    });\n  };\n  const handleApplyChanges = () => {\n    updateChecklistTitleSubTitle(titleSubTitleProperties);\n    // Update the initial state to the current state after applying changes\n    setInitialState({\n      ...titleSubTitleProperties\n    });\n    // Reset the changes flag\n    setHasChanges(false);\n    // Disable the Apply button\n    setIsDisabled(true);\n    // Close the popup\n    setTitlePopup(false);\n    // Mark changes as unsaved\n    setIsUnSavedChanges(true);\n  };\n  const handleClose = () => {\n    setTitlePopup(false);\n  };\n  const handledesignclose = () => {\n    setDesignPopup(false);\n  };\n  const handleBlur = () => {\n    // If the title is empty, restore the previous title\n    if (titleSubTitleProperties.title.trim() === \"\") {\n      setTitleSubTitleProperties(prevState => ({\n        ...prevState,\n        title: tempTitle // Reset to the default value\n      }));\n    }\n    if (titleSubTitleProperties.subTitle.trim() === \"\") {\n      setTitleSubTitleProperties(prevState => ({\n        ...prevState,\n        subTitle: tempSubTitle // Reset to the default value\n      }));\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    id: \"qadpt-designpopup\",\n    className: \"qadpt-designpopup\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadpt-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-design-header\",\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          \"aria-label\": \"back\",\n          onClick: onBack,\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIosNewOutlinedIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-title\",\n          children: translate(\"Title & SubTitle\", {\n            defaultValue: \"Title & SubTitle\"\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"small\",\n          \"aria-label\": \"close\",\n          onClick: onClose,\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 7\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 6\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-canblock\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-controls qadpt-errmsg\",\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              backgroundColor: \"#EAE2E2\",\n              borderRadius: \"var(--button-border-radius)\",\n              padding: \"8px\",\n              marginBottom: \"5px\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              sx: {\n                fontWeight: \"600\",\n                paddingBottom: \"5px\",\n                textAlign: \"left\"\n              },\n              children: translate(\"Title\", {\n                defaultValue: \"Title\"\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              sx: {\n                padding: \"0 !important\",\n                height: \"auto !important\"\n              },\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                variant: \"outlined\",\n                size: \"small\",\n                placeholder: translate(\"Checklist Title\", {\n                  defaultValue: \"Checklist Title\"\n                }),\n                className: \"qadpt-control-input\",\n                value: titleSubTitleProperties.title,\n                style: {\n                  width: \"100%\"\n                },\n                onChange: handleTitleChange,\n                error: Boolean(titleError) // Show error if message exists\n                ,\n                helperText: titleError ? /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    display: \"flex\",\n                    fontSize: \"12px\",\n                    alignItems: \"center\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      marginRight: \"4px\"\n                    },\n                    dangerouslySetInnerHTML: {\n                      __html: warning\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 223,\n                    columnNumber: 14\n                  }, this), translate(titleError, {\n                    defaultValue: titleError\n                  })]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 13\n                }, this) : null,\n                InputProps: {\n                  sx: {\n                    \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"& fieldset\": {\n                      border: \"none\"\n                    },\n                    \"& input\": {\n                      textAlign: \"left !important\",\n                      paddingLeft: \"10px !important\"\n                    },\n                    \"&.MuiInputBase-root\": {\n                      height: \"auto !important\",\n                      marginBottom: \"5px\"\n                    }\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 10\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              sx: {\n                backgroundColor: \"#E5DADA !important\",\n                height: \"35px !important\",\n                borderRadius: \"6px !important\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                children: translate(\"Title Color\", {\n                  defaultValue: \"Title Color\"\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"color\",\n                  value: titleSubTitleProperties.titleColor,\n                  onChange: e => onPropertyChange(\"titleColor\", e.target.value),\n                  className: \"qadpt-color-input\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              sx: {\n                backgroundColor: \"#E5DADA !important\",\n                height: \"35px !important\",\n                borderRadius: \"6px !important\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                children: translate(\"Bold\", {\n                  defaultValue: \"Bold\"\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"toggle-switch qadpt-toggle-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: titleSubTitleProperties.titleBold,\n                    onChange: e => onPropertyChange(\"titleBold\", e.target.checked),\n                    name: \"toggleSwitch\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 273,\n                    columnNumber: 12\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"slider\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 279,\n                    columnNumber: 12\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              sx: {\n                backgroundColor: \"#E5DADA !important\",\n                height: \"35px !important\",\n                borderRadius: \"6px !important\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                children: [translate(\"Italic\", {\n                  defaultValue: \"Italic\"\n                }), \" \"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"toggle-switch qadpt-toggle-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: titleSubTitleProperties.titleItalic,\n                    onChange: e => onPropertyChange(\"titleItalic\", e.target.checked),\n                    name: \"toggleSwitch\"\n                    //disabled={(selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") && status}\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 295,\n                    columnNumber: 12\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"slider\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 302,\n                    columnNumber: 12\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              backgroundColor: \"#EAE2E2\",\n              borderRadius: \"var(--button-border-radius)\",\n              height: \"auto\",\n              padding: \"8px\",\n              marginBottom: \"5px\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              sx: {\n                fontWeight: \"600\",\n                paddingBottom: \"5px\",\n                textAlign: \"left\"\n              },\n              children: translate(\"Sub Title\", {\n                defaultValue: \"Sub Title\"\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              sx: {\n                padding: \"0 !important\",\n                height: \"auto !important\"\n              },\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                variant: \"outlined\",\n                size: \"small\",\n                placeholder: translate(\"Checklist Title\", {\n                  defaultValue: \"Checklist Title\"\n                }),\n                className: \"qadpt-control-input\",\n                value: titleSubTitleProperties.subTitle,\n                style: {\n                  width: \"100%\"\n                },\n                onChange: handleSubTitleChange,\n                error: Boolean(subtitleError) // Show error if message exists\n                ,\n                helperText: subtitleError ? /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    display: \"flex\",\n                    fontSize: \"12px\",\n                    alignItems: \"center\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      marginRight: \"4px\"\n                    },\n                    dangerouslySetInnerHTML: {\n                      __html: warning\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 335,\n                    columnNumber: 14\n                  }, this), translate(subtitleError, {\n                    defaultValue: subtitleError\n                  })]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 13\n                }, this) : null,\n                InputProps: {\n                  endAdornment: \"\",\n                  sx: {\n                    \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"& fieldset\": {\n                      border: \"none\"\n                    },\n                    \"& input\": {\n                      textAlign: \"left !important\",\n                      paddingLeft: \"10px !important\"\n                    },\n                    \"&.MuiInputBase-root\": {\n                      height: \"auto !important\",\n                      marginBottom: \"5px\"\n                    }\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 10\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              sx: {\n                backgroundColor: \"#E5DADA !important\",\n                height: \"35px !important\",\n                borderRadius: \"6px !important\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                children: translate(\"SubTitle Color\", {\n                  defaultValue: \"SubTitle Color\"\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"color\",\n                  value: titleSubTitleProperties.subTitleColor,\n                  onChange: e => onPropertyChange(\"subTitleColor\", e.target.value),\n                  className: \"qadpt-color-input\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 366,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 365,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              sx: {\n                backgroundColor: \"#E5DADA !important\",\n                height: \"35px !important\",\n                borderRadius: \"6px !important\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                children: translate(\"Bold\", {\n                  defaultValue: \"Bold\"\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"toggle-switch \",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: titleSubTitleProperties.subTitleBold,\n                    onChange: e => onPropertyChange(\"subTitleBold\", e.target.checked),\n                    name: \"toggleSwitch\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 386,\n                    columnNumber: 12\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"slider\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 392,\n                    columnNumber: 12\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              sx: {\n                backgroundColor: \"#E5DADA !important\",\n                height: \"35px !important\",\n                borderRadius: \"6px !important\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                children: [translate(\"Italic\", {\n                  defaultValue: \"Italic\"\n                }), \" \"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"toggle-switch qadpt-toggle-group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: titleSubTitleProperties.subTitleItalic,\n                    onChange: e => onPropertyChange(\"subTitleItalic\", e.target.checked),\n                    name: \"toggleSwitch\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 408,\n                    columnNumber: 12\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"slider\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 414,\n                    columnNumber: 12\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 8\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 7\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 6\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-drawerFooter\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: handleApplyChanges,\n          className: `qadpt-btn ${isDisabled ? \"disabled\" : \"\"}`,\n          disabled: isDisabled,\n          children: translate(\"Apply\", {\n            defaultValue: \"Apply\"\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 7\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 421,\n        columnNumber: 6\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 5\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 174,\n    columnNumber: 4\n  }, this);\n};\n_s(TitleSubTitle, \"cgyBnNCsotlrJrYKqB4gMLErYIw=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c = TitleSubTitle;\nexport default TitleSubTitle;\nvar _c;\n$RefreshReg$(_c, \"TitleSubTitle\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useTranslation", "Box", "Typography", "TextField", "IconButton", "<PERSON><PERSON>", "CloseIcon", "useDrawerStore", "warning", "ArrowBackIosNewOutlinedIcon", "jsxDEV", "_jsxDEV", "TitleSubTitle", "currentGuide", "onBack", "onClose", "_s", "t", "translate", "titlePopup", "setTitlePopup", "setDesignPopup", "titleColor", "setTitleColor", "checklistTitle", "setChecklistTitle", "checklistSubTitle", "setChecklistSubTitle", "checklistGuideMetaData", "updateChecklistTitleSubTitle", "setIsUnSavedChanges", "isUnSavedChanges", "state", "titleError", "setTitleError", "subtitleError", "setsubTitleError", "isDisabled", "setIsDisabled", "has<PERSON><PERSON><PERSON>", "set<PERSON>as<PERSON><PERSON><PERSON>", "getLocalizedDefaults", "existing", "_existing$titleBold", "_existing$titleItalic", "_existing$subTitleBol", "_existing$subTitleIta", "defaultTitle", "defaultSubTitle", "title", "defaultValue", "subTitle", "titleBold", "titleItalic", "subTitleColor", "subTitleBold", "subTitleItalic", "updateApplyButtonState", "titleErr", "subtitleErr", "changed", "handleTitleChange", "e", "value", "target", "errorMessage", "length", "onPropertyChange", "handleSubTitleChange", "titleSubTitleProperties", "setTitleSubTitleProperties", "_checklistGuideMetaDa", "tempTitle", "setTempTitle", "tempSubTitle", "settempTempTitle", "initialState", "setInitialState", "hasAnyChanges", "Object", "keys", "some", "key", "handleTitleColorChange", "prevState", "handleApplyChanges", "handleClose", "handledesignclose", "handleBlur", "trim", "id", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "sx", "backgroundColor", "borderRadius", "padding", "marginBottom", "fontWeight", "paddingBottom", "textAlign", "height", "variant", "placeholder", "style", "width", "onChange", "error", "Boolean", "helperText", "display", "fontSize", "alignItems", "marginRight", "dangerouslySetInnerHTML", "__html", "InputProps", "border", "paddingLeft", "type", "checked", "name", "endAdornment", "disabled", "_c", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/checklist/TitleSubTitle.tsx"], "sourcesContent": ["import React, { useReducer, useState,useEffect } from \"react\";\r\nimport { useTranslation } from \"react-i18next\";\r\nimport { Box, Typography, TextField, Grid, IconButton, Button, InputAdornment, FormControl, InputLabel, Select, MenuItem, SelectChangeEvent, FormControlLabel, Switch } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport useDrawerStore, { BUTTON_CONT_DEF_VALUE_1, CANVAS_DEFAULT_VALUE, IMG_CONT_DEF_VALUE } from \"../../store/drawerStore\";\r\nimport { HOTSPOT_DEFAULT_VALUE } from \"../../store/drawerStore\";\r\nimport {\r\n  InfoFilled,\r\n  QuestionFill,\r\n  Reselect,\r\n  Solid,\r\n  warning,\r\n} from \"../../assets/icons/icons\";\r\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\r\n\r\nconst TitleSubTitle = ({ currentGuide,onBack,onClose }: any) => {\r\n\tconst { t: translate } = useTranslation();\r\n    const {\r\n\t\t\ttitlePopup,\r\n\t\t\tsetTitlePopup,\r\n\t\t\tsetDesignPopup,\r\n\t\t\ttitleColor,\r\n\t\t\tsetTitleColor,\r\n\t\t\tchecklistTitle,\r\n\t\t\tsetChecklistTitle,\r\n\t\t\tchecklistSubTitle,\r\n\t\t\tsetChecklistSubTitle,\r\n\t\t\tchecklistGuideMetaData,\r\n\t\t\tupdateChecklistTitleSubTitle,\r\n\t\t\tsetIsUnSavedChanges,\r\n\t\t\tisUnSavedChanges,\r\n\t\t} = useDrawerStore((state: any) => state);\r\n\t\tconst [titleError, setTitleError] = useState(\"\"); // Store error message\r\n\t\tconst [subtitleError, setsubTitleError] = useState(\"\"); // Store error message\r\n\t\tconst [isDisabled, setIsDisabled] = useState(true);\r\n\t\tconst [hasChanges, setHasChanges] = useState(false); // Track if any changes were made\r\n\r\n\t\r\nfunction getLocalizedDefaults(t: any, existing: any = {}): any {\r\n  const defaultTitle = \"Checklist Title\";\r\n  const defaultSubTitle = \"Context about the tasks in the checklist below users should prioritize completing.\";\r\n\r\n  return {\r\n    ...existing,\r\n    title:\r\n      existing?.title === defaultTitle || !existing?.title\r\n        ? t(defaultTitle, { defaultValue: defaultTitle })\r\n        : existing?.title,\r\n    subTitle:\r\n      existing?.subTitle === defaultSubTitle || !existing?.subTitle\r\n        ? t(defaultSubTitle, { defaultValue: defaultSubTitle })\r\n        : existing?.subTitle,\r\n    titleColor: existing?.titleColor || \"#333\",\r\n    titleBold: existing?.titleBold ?? true,\r\n    titleItalic: existing?.titleItalic ?? false,\r\n    subTitleColor: existing?.subTitleColor || \"#8D8D8D\",\r\n    subTitleBold: existing?.subTitleBold ?? false,\r\n    subTitleItalic: existing?.subTitleItalic ?? false,\r\n  };\r\n}\r\n\t\r\n\t\t// Function to check if the Apply button should be enabled\r\n\t\tconst updateApplyButtonState = (titleErr: string, subtitleErr: string, changed: boolean) => {\r\n\t\t\t// Enable the button if there are no errors AND changes have been made\r\n\t\t\tsetIsDisabled(!!titleErr || !!subtitleErr || !changed);\r\n\t\t};\r\n\r\n\t\tconst handleTitleChange = (e: any) => {\r\n\t\t\tconst value = e.target.value;\r\n\t\t\tlet errorMessage = \"\";\r\n\r\n\t\t\tif (value.length < 5) {\r\n\t\t\t\terrorMessage = translate(\"Min: 5 Characters\");\r\n\t\t\t} else if (value.length > 50) {\r\n\t\t\t\terrorMessage = translate(\"Max: 50 Characters\");\r\n\t\t\t}\r\n\r\n\t\t\tsetTitleError(errorMessage);\r\n\t\t\tsetHasChanges(true); // Mark that changes have been made\r\n\t\t\tupdateApplyButtonState(errorMessage, subtitleError, true);\r\n\t\t\tonPropertyChange(\"title\", value);\r\n\t\t};\r\n\r\n\t\tconst handleSubTitleChange = (e: any) => {\r\n\t\t\tconst value = e.target.value;\r\n\t\t\tlet errorMessage = \"\";\r\n\r\n\t\t\tif (value.length < 5) {\r\n\t\t\t\terrorMessage = translate(\"Min: 5 Characters\");\r\n\t\t\t} else if (value.length > 500) {\r\n\t\t\t\terrorMessage = translate(\"Max: 500 Characters\");\r\n\t\t\t}\r\n\r\n\t\t\tsetsubTitleError(errorMessage);\r\n\t\t\tsetHasChanges(true); // Mark that changes have been made\r\n\t\t\tupdateApplyButtonState(titleError, errorMessage, true);\r\n\t\t\tonPropertyChange(\"subTitle\", value);\r\n\t\t};\r\n\r\n\t\tconst [titleSubTitleProperties, setTitleSubTitleProperties] = useState(() =>\r\n\t\t\tgetLocalizedDefaults(translate, checklistGuideMetaData[0]?.TitleSubTitle)\r\n);\r\n\r\n\t\tconst [tempTitle, setTempTitle] = useState(titleSubTitleProperties?.title);\r\n\t\tconst [tempSubTitle, settempTempTitle] = useState(titleSubTitleProperties?.subTitle);\r\n\r\n\t\t// Store the initial state to compare for changes\r\n\t\tconst [initialState, setInitialState] = useState(titleSubTitleProperties);\r\n\r\n\t\t// Effect to check for any changes compared to initial state\r\n\t\tuseEffect(() => {\r\n\t\t\t// Compare current properties with initial state\r\n\t\t\tconst hasAnyChanges = Object.keys(titleSubTitleProperties).some(\r\n\t\t\t\t(key) => titleSubTitleProperties[key] !== initialState[key]\r\n\t\t\t);\r\n\r\n\t\t\tsetHasChanges(hasAnyChanges);\r\n\t\t\tupdateApplyButtonState(titleError, subtitleError, hasAnyChanges);\r\n\t\t}, [titleSubTitleProperties, initialState, titleError, subtitleError]);\r\n\t\tconst handleTitleColorChange = (e: any) => setTitleColor(e.target.value);\r\n\r\n\t\tconst onPropertyChange = (key: any, value: any) => {\r\n\t\t\tsetTitleSubTitleProperties((prevState: any) => {\r\n\t\t\t\t// Check if the value has actually changed\r\n\t\t\t\tif (prevState[key] !== value) {\r\n\t\t\t\t\tsetHasChanges(true);\r\n\t\t\t\t\tupdateApplyButtonState(titleError, subtitleError, true);\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn {\r\n\t\t\t\t\t...prevState,\r\n\t\t\t\t\t[key]: value,\r\n\t\t\t\t};\r\n\t\t\t});\r\n\t\t};\r\n\r\n\t\tconst handleApplyChanges = () => {\r\n\t\t\tupdateChecklistTitleSubTitle(titleSubTitleProperties);\r\n\t\t\t// Update the initial state to the current state after applying changes\r\n\t\t\tsetInitialState({ ...titleSubTitleProperties });\r\n\t\t\t// Reset the changes flag\r\n\t\t\tsetHasChanges(false);\r\n\t\t\t// Disable the Apply button\r\n\t\t\tsetIsDisabled(true);\r\n\t\t\t// Close the popup\r\n\t\t\tsetTitlePopup(false);\r\n\t\t\t// Mark changes as unsaved\r\n\t\t\tsetIsUnSavedChanges(true);\r\n\t\t};\r\n\r\n\t\tconst handleClose = () => {\r\n\t\t\tsetTitlePopup(false);\r\n\t\t};\r\n\t\tconst handledesignclose = () => {\r\n\t\t\tsetDesignPopup(false);\r\n\t\t};\r\n\t\tconst handleBlur = () => {\r\n\t\t\t// If the title is empty, restore the previous title\r\n\t\t\tif (titleSubTitleProperties.title.trim() === \"\") {\r\n\t\t\t\tsetTitleSubTitleProperties((prevState: any) => ({\r\n\t\t\t\t\t...prevState,\r\n\t\t\t\t\ttitle: tempTitle, // Reset to the default value\r\n\t\t\t\t}));\r\n\t\t\t}\r\n\t\t\tif (titleSubTitleProperties.subTitle.trim() === \"\") {\r\n\t\t\t\tsetTitleSubTitleProperties((prevState: any) => ({\r\n\t\t\t\t\t...prevState,\r\n\t\t\t\t\tsubTitle: tempSubTitle, // Reset to the default value\r\n\t\t\t\t}));\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\treturn (\r\n\t\t\t<div\r\n\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\tclassName=\"qadpt-designpopup\"\r\n\t\t\t>\r\n\t\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\taria-label=\"back\"\r\n\t\t\t\t\t\t\tonClick={onBack}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<ArrowBackIosNewOutlinedIcon />\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t<div className=\"qadpt-title\">{translate(\"Title & SubTitle\", { defaultValue: \"Title & SubTitle\" })}</div>\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\t\tonClick={onClose}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div className=\"qadpt-canblock\">\r\n\t\t\t\t\t\t<div className=\"qadpt-controls qadpt-errmsg\">\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"#EAE2E2\",\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"var(--button-border-radius)\",\r\n\t\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Typography sx={{ fontWeight: \"600\", paddingBottom: \"5px\", textAlign: \"left\" }}>{translate(\"Title\", { defaultValue: \"Title\" })}</Typography>\r\n\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\tsx={{ padding: \"0 !important\", height: \"auto !important\" }}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\tplaceholder={translate(\"Checklist Title\", { defaultValue: \"Checklist Title\" })}\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\t\tvalue={titleSubTitleProperties.title}\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ width: \"100%\" }}\r\n\t\t\t\t\t\t\t\t\t\tonChange={handleTitleChange}\r\n\t\t\t\t\t\t\t\t\t\terror={Boolean(titleError)} // Show error if message exists\r\n\t\t\t\t\t\t\t\t\t\thelperText={\r\n\t\t\t\t\t\t\t\t\t\t\ttitleError ? (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ marginRight: \"4px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{translate(titleError, { defaultValue: titleError })}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t\t\t) : null\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"& input\": { textAlign: \"left !important\", paddingLeft: \"10px !important\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"&.MuiInputBase-root\": { height: \"auto !important\", marginBottom: \"5px\" },\r\n\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#E5DADA !important\",\r\n\t\t\t\t\t\t\t\t\t\theight: \"35px !important\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"6px !important\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Title Color\", { defaultValue: \"Title Color\" })}</div>\r\n\t\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\t\t\t\tvalue={titleSubTitleProperties.titleColor}\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"titleColor\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#E5DADA !important\",\r\n\t\t\t\t\t\t\t\t\t\theight: \"35px !important\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"6px !important\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Bold\", { defaultValue: \"Bold\" })}</div>\r\n\t\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t\t<label className=\"toggle-switch qadpt-toggle-group\">\r\n\t\t\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\t\t\ttype=\"checkbox\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tchecked={titleSubTitleProperties.titleBold}\r\n\t\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"titleBold\", e.target.checked)}\r\n\t\t\t\t\t\t\t\t\t\t\t\tname=\"toggleSwitch\"\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t<span className=\"slider\"></span>\r\n\t\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#E5DADA !important\",\r\n\t\t\t\t\t\t\t\t\t\theight: \"35px !important\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"6px !important\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Italic\", { defaultValue: \"Italic\" })} </div>\r\n\t\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t\t<label className=\"toggle-switch qadpt-toggle-group\">\r\n\t\t\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\t\t\ttype=\"checkbox\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tchecked={titleSubTitleProperties.titleItalic}\r\n\t\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"titleItalic\", e.target.checked)}\r\n\t\t\t\t\t\t\t\t\t\t\t\tname=\"toggleSwitch\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t//disabled={(selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") && status}\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t<span className=\"slider\"></span>\r\n\t\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"#EAE2E2\",\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"var(--button-border-radius)\",\r\n\t\t\t\t\t\t\t\t\theight: \"auto\",\r\n\t\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Typography sx={{ fontWeight: \"600\", paddingBottom: \"5px\", textAlign: \"left\" }}>{translate(\"Sub Title\", { defaultValue: \"Sub Title\" })}</Typography>\r\n\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\tsx={{ padding: \"0 !important\", height: \"auto !important\" }}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\tplaceholder={translate(\"Checklist Title\", { defaultValue: \"Checklist Title\" })}\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\t\tvalue={titleSubTitleProperties.subTitle}\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ width: \"100%\" }}\r\n\t\t\t\t\t\t\t\t\t\tonChange={handleSubTitleChange}\r\n\t\t\t\t\t\t\t\t\t\terror={Boolean(subtitleError)} // Show error if message exists\r\n\t\t\t\t\t\t\t\t\t\thelperText={\r\n\t\t\t\t\t\t\t\t\t\t\tsubtitleError ? (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ marginRight: \"4px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{translate(subtitleError, { defaultValue: subtitleError })}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t\t\t) : null\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\t\tendAdornment: \"\",\r\n\t\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"& input\": { textAlign: \"left !important\", paddingLeft: \"10px !important\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"&.MuiInputBase-root\": { height: \"auto !important\", marginBottom: \"5px\" },\r\n\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#E5DADA !important\",\r\n\t\t\t\t\t\t\t\t\t\theight: \"35px !important\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"6px !important\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"SubTitle Color\", { defaultValue: \"SubTitle Color\" })}</div>\r\n\t\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\t\t\t\tvalue={titleSubTitleProperties.subTitleColor}\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"subTitleColor\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#E5DADA !important\",\r\n\t\t\t\t\t\t\t\t\t\theight: \"35px !important\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"6px !important\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Bold\", { defaultValue: \"Bold\" })}</div>\r\n\t\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t\t<label className=\"toggle-switch \">\r\n\t\t\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\t\t\ttype=\"checkbox\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tchecked={titleSubTitleProperties.subTitleBold}\r\n\t\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"subTitleBold\", e.target.checked)}\r\n\t\t\t\t\t\t\t\t\t\t\t\tname=\"toggleSwitch\"\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t<span className=\"slider\"></span>\r\n\t\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#E5DADA !important\",\r\n\t\t\t\t\t\t\t\t\t\theight: \"35px !important\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"6px !important\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Italic\", { defaultValue: \"Italic\" })} </div>\r\n\t\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t\t<label className=\"toggle-switch qadpt-toggle-group\">\r\n\t\t\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\t\t\ttype=\"checkbox\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tchecked={titleSubTitleProperties.subTitleItalic}\r\n\t\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"subTitleItalic\", e.target.checked)}\r\n\t\t\t\t\t\t\t\t\t\t\t\tname=\"toggleSwitch\"\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t<span className=\"slider\"></span>\r\n\t\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div className=\"qadpt-drawerFooter\">\r\n\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\tonClick={handleApplyChanges}\r\n\t\t\t\t\t\t\tclassName={`qadpt-btn ${isDisabled ? \"disabled\" : \"\"}`}\r\n\t\t\t\t\t\t\tdisabled={isDisabled}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{translate(\"Apply\", { defaultValue: \"Apply\" })}\r\n\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t);\r\n};\r\n\r\nexport default TitleSubTitle;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAgBC,QAAQ,EAACC,SAAS,QAAQ,OAAO;AAC7D,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAASC,GAAG,EAAEC,UAAU,EAAEC,SAAS,EAAQC,UAAU,EAAEC,MAAM,QAAgH,eAAe;AAC5L,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,cAAc,MAA6E,yBAAyB;AAE3H,SAKEC,OAAO,QACF,0BAA0B;AACjC,OAAOC,2BAA2B,MAAM,6CAA6C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtF,MAAMC,aAAa,GAAGA,CAAC;EAAEC,YAAY;EAACC,MAAM;EAACC;AAAa,CAAC,KAAK;EAAAC,EAAA;EAC/D,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGlB,cAAc,CAAC,CAAC;EACtC,MAAM;IACPmB,UAAU;IACVC,aAAa;IACbC,cAAc;IACdC,UAAU;IACVC,aAAa;IACbC,cAAc;IACdC,iBAAiB;IACjBC,iBAAiB;IACjBC,oBAAoB;IACpBC,sBAAsB;IACtBC,4BAA4B;IAC5BC,mBAAmB;IACnBC;EACD,CAAC,GAAGxB,cAAc,CAAEyB,KAAU,IAAKA,KAAK,CAAC;EACzC,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAClD,MAAM,CAACqC,aAAa,EAAEC,gBAAgB,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACxD,MAAM,CAACuC,UAAU,EAAEC,aAAa,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACyC,UAAU,EAAEC,aAAa,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAGvD,SAAS2C,oBAAoBA,CAACxB,CAAM,EAAEyB,QAAa,GAAG,CAAC,CAAC,EAAO;IAAA,IAAAC,mBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;IAC7D,MAAMC,YAAY,GAAG,iBAAiB;IACtC,MAAMC,eAAe,GAAG,oFAAoF;IAE5G,OAAO;MACL,GAAGN,QAAQ;MACXO,KAAK,EACH,CAAAP,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEO,KAAK,MAAKF,YAAY,IAAI,EAACL,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEO,KAAK,IAChDhC,CAAC,CAAC8B,YAAY,EAAE;QAAEG,YAAY,EAAEH;MAAa,CAAC,CAAC,GAC/CL,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEO,KAAK;MACrBE,QAAQ,EACN,CAAAT,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAES,QAAQ,MAAKH,eAAe,IAAI,EAACN,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAES,QAAQ,IACzDlC,CAAC,CAAC+B,eAAe,EAAE;QAAEE,YAAY,EAAEF;MAAgB,CAAC,CAAC,GACrDN,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAES,QAAQ;MACxB7B,UAAU,EAAE,CAAAoB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEpB,UAAU,KAAI,MAAM;MAC1C8B,SAAS,GAAAT,mBAAA,GAAED,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEU,SAAS,cAAAT,mBAAA,cAAAA,mBAAA,GAAI,IAAI;MACtCU,WAAW,GAAAT,qBAAA,GAAEF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEW,WAAW,cAAAT,qBAAA,cAAAA,qBAAA,GAAI,KAAK;MAC3CU,aAAa,EAAE,CAAAZ,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEY,aAAa,KAAI,SAAS;MACnDC,YAAY,GAAAV,qBAAA,GAAEH,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEa,YAAY,cAAAV,qBAAA,cAAAA,qBAAA,GAAI,KAAK;MAC7CW,cAAc,GAAAV,qBAAA,GAAEJ,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEc,cAAc,cAAAV,qBAAA,cAAAA,qBAAA,GAAI;IAC9C,CAAC;EACH;;EAEE;EACA,MAAMW,sBAAsB,GAAGA,CAACC,QAAgB,EAAEC,WAAmB,EAAEC,OAAgB,KAAK;IAC3F;IACAtB,aAAa,CAAC,CAAC,CAACoB,QAAQ,IAAI,CAAC,CAACC,WAAW,IAAI,CAACC,OAAO,CAAC;EACvD,CAAC;EAED,MAAMC,iBAAiB,GAAIC,CAAM,IAAK;IACrC,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACD,KAAK;IAC5B,IAAIE,YAAY,GAAG,EAAE;IAErB,IAAIF,KAAK,CAACG,MAAM,GAAG,CAAC,EAAE;MACrBD,YAAY,GAAG/C,SAAS,CAAC,mBAAmB,CAAC;IAC9C,CAAC,MAAM,IAAI6C,KAAK,CAACG,MAAM,GAAG,EAAE,EAAE;MAC7BD,YAAY,GAAG/C,SAAS,CAAC,oBAAoB,CAAC;IAC/C;IAEAgB,aAAa,CAAC+B,YAAY,CAAC;IAC3BzB,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;IACrBiB,sBAAsB,CAACQ,YAAY,EAAE9B,aAAa,EAAE,IAAI,CAAC;IACzDgC,gBAAgB,CAAC,OAAO,EAAEJ,KAAK,CAAC;EACjC,CAAC;EAED,MAAMK,oBAAoB,GAAIN,CAAM,IAAK;IACxC,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACD,KAAK;IAC5B,IAAIE,YAAY,GAAG,EAAE;IAErB,IAAIF,KAAK,CAACG,MAAM,GAAG,CAAC,EAAE;MACrBD,YAAY,GAAG/C,SAAS,CAAC,mBAAmB,CAAC;IAC9C,CAAC,MAAM,IAAI6C,KAAK,CAACG,MAAM,GAAG,GAAG,EAAE;MAC9BD,YAAY,GAAG/C,SAAS,CAAC,qBAAqB,CAAC;IAChD;IAEAkB,gBAAgB,CAAC6B,YAAY,CAAC;IAC9BzB,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;IACrBiB,sBAAsB,CAACxB,UAAU,EAAEgC,YAAY,EAAE,IAAI,CAAC;IACtDE,gBAAgB,CAAC,UAAU,EAAEJ,KAAK,CAAC;EACpC,CAAC;EAED,MAAM,CAACM,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGxE,QAAQ,CAAC;IAAA,IAAAyE,qBAAA;IAAA,OACtE9B,oBAAoB,CAACvB,SAAS,GAAAqD,qBAAA,GAAE3C,sBAAsB,CAAC,CAAC,CAAC,cAAA2C,qBAAA,uBAAzBA,qBAAA,CAA2B3D,aAAa,CAAC;EAAA,CAC5E,CAAC;EAEC,MAAM,CAAC4D,SAAS,EAAEC,YAAY,CAAC,GAAG3E,QAAQ,CAACuE,uBAAuB,aAAvBA,uBAAuB,uBAAvBA,uBAAuB,CAAEpB,KAAK,CAAC;EAC1E,MAAM,CAACyB,YAAY,EAAEC,gBAAgB,CAAC,GAAG7E,QAAQ,CAACuE,uBAAuB,aAAvBA,uBAAuB,uBAAvBA,uBAAuB,CAAElB,QAAQ,CAAC;;EAEpF;EACA,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAG/E,QAAQ,CAACuE,uBAAuB,CAAC;;EAEzE;EACAtE,SAAS,CAAC,MAAM;IACf;IACA,MAAM+E,aAAa,GAAGC,MAAM,CAACC,IAAI,CAACX,uBAAuB,CAAC,CAACY,IAAI,CAC7DC,GAAG,IAAKb,uBAAuB,CAACa,GAAG,CAAC,KAAKN,YAAY,CAACM,GAAG,CAC3D,CAAC;IAED1C,aAAa,CAACsC,aAAa,CAAC;IAC5BrB,sBAAsB,CAACxB,UAAU,EAAEE,aAAa,EAAE2C,aAAa,CAAC;EACjE,CAAC,EAAE,CAACT,uBAAuB,EAAEO,YAAY,EAAE3C,UAAU,EAAEE,aAAa,CAAC,CAAC;EACtE,MAAMgD,sBAAsB,GAAIrB,CAAM,IAAKvC,aAAa,CAACuC,CAAC,CAACE,MAAM,CAACD,KAAK,CAAC;EAExE,MAAMI,gBAAgB,GAAGA,CAACe,GAAQ,EAAEnB,KAAU,KAAK;IAClDO,0BAA0B,CAAEc,SAAc,IAAK;MAC9C;MACA,IAAIA,SAAS,CAACF,GAAG,CAAC,KAAKnB,KAAK,EAAE;QAC7BvB,aAAa,CAAC,IAAI,CAAC;QACnBiB,sBAAsB,CAACxB,UAAU,EAAEE,aAAa,EAAE,IAAI,CAAC;MACxD;MAEA,OAAO;QACN,GAAGiD,SAAS;QACZ,CAACF,GAAG,GAAGnB;MACR,CAAC;IACF,CAAC,CAAC;EACH,CAAC;EAED,MAAMsB,kBAAkB,GAAGA,CAAA,KAAM;IAChCxD,4BAA4B,CAACwC,uBAAuB,CAAC;IACrD;IACAQ,eAAe,CAAC;MAAE,GAAGR;IAAwB,CAAC,CAAC;IAC/C;IACA7B,aAAa,CAAC,KAAK,CAAC;IACpB;IACAF,aAAa,CAAC,IAAI,CAAC;IACnB;IACAlB,aAAa,CAAC,KAAK,CAAC;IACpB;IACAU,mBAAmB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMwD,WAAW,GAAGA,CAAA,KAAM;IACzBlE,aAAa,CAAC,KAAK,CAAC;EACrB,CAAC;EACD,MAAMmE,iBAAiB,GAAGA,CAAA,KAAM;IAC/BlE,cAAc,CAAC,KAAK,CAAC;EACtB,CAAC;EACD,MAAMmE,UAAU,GAAGA,CAAA,KAAM;IACxB;IACA,IAAInB,uBAAuB,CAACpB,KAAK,CAACwC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAChDnB,0BAA0B,CAAEc,SAAc,KAAM;QAC/C,GAAGA,SAAS;QACZnC,KAAK,EAAEuB,SAAS,CAAE;MACnB,CAAC,CAAC,CAAC;IACJ;IACA,IAAIH,uBAAuB,CAAClB,QAAQ,CAACsC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACnDnB,0BAA0B,CAAEc,SAAc,KAAM;QAC/C,GAAGA,SAAS;QACZjC,QAAQ,EAAEuB,YAAY,CAAE;MACzB,CAAC,CAAC,CAAC;IACJ;EACD,CAAC;EAED,oBACC/D,OAAA;IACC+E,EAAE,EAAC,mBAAmB;IACtBC,SAAS,EAAC,mBAAmB;IAAAC,QAAA,eAE7BjF,OAAA;MAAKgF,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC7BjF,OAAA;QAAKgF,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBACnCjF,OAAA,CAACP,UAAU;UACV,cAAW,MAAM;UACjByF,OAAO,EAAE/E,MAAO;UAAA8E,QAAA,eAEhBjF,OAAA,CAACF,2BAA2B;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACbtF,OAAA;UAAKgF,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAE1E,SAAS,CAAC,kBAAkB,EAAE;YAAEgC,YAAY,EAAE;UAAmB,CAAC;QAAC;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxGtF,OAAA,CAACP,UAAU;UACV8F,IAAI,EAAC,OAAO;UACZ,cAAW,OAAO;UAClBL,OAAO,EAAE9E,OAAQ;UAAA6E,QAAA,eAEjBjF,OAAA,CAACL,SAAS;YAAAwF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACNtF,OAAA;QAAKgF,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC9BjF,OAAA;UAAKgF,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC3CjF,OAAA,CAACV,GAAG;YACHkG,EAAE,EAAE;cACHC,eAAe,EAAE,SAAS;cAC1BC,YAAY,EAAE,6BAA6B;cAC3CC,OAAO,EAAE,KAAK;cACdC,YAAY,EAAE;YACf,CAAE;YAAAX,QAAA,gBAEFjF,OAAA,CAACT,UAAU;cAACiG,EAAE,EAAE;gBAAEK,UAAU,EAAE,KAAK;gBAAEC,aAAa,EAAE,KAAK;gBAAEC,SAAS,EAAE;cAAO,CAAE;cAAAd,QAAA,EAAE1E,SAAS,CAAC,OAAO,EAAE;gBAAEgC,YAAY,EAAE;cAAQ,CAAC;YAAC;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAE5ItF,OAAA,CAACV,GAAG;cACH0F,SAAS,EAAC,mBAAmB;cAC7BQ,EAAE,EAAE;gBAAEG,OAAO,EAAE,cAAc;gBAAEK,MAAM,EAAE;cAAkB,CAAE;cAAAf,QAAA,eAE3DjF,OAAA,CAACR,SAAS;gBACTyG,OAAO,EAAC,UAAU;gBAClBV,IAAI,EAAC,OAAO;gBACZW,WAAW,EAAE3F,SAAS,CAAC,iBAAiB,EAAE;kBAAEgC,YAAY,EAAE;gBAAkB,CAAC,CAAE;gBAC/EyC,SAAS,EAAC,qBAAqB;gBAC/B5B,KAAK,EAAEM,uBAAuB,CAACpB,KAAM;gBACrC6D,KAAK,EAAE;kBAAEC,KAAK,EAAE;gBAAO,CAAE;gBACzBC,QAAQ,EAAEnD,iBAAkB;gBAC5BoD,KAAK,EAAEC,OAAO,CAACjF,UAAU,CAAE,CAAC;gBAAA;gBAC5BkF,UAAU,EACTlF,UAAU,gBACTtB,OAAA;kBAAMmG,KAAK,EAAE;oBAAEM,OAAO,EAAE,MAAM;oBAAEC,QAAQ,EAAE,MAAM;oBAAEC,UAAU,EAAE;kBAAS,CAAE;kBAAA1B,QAAA,gBACxEjF,OAAA;oBACCmG,KAAK,EAAE;sBAAES,WAAW,EAAE;oBAAM,CAAE;oBAC9BC,uBAAuB,EAAE;sBAAEC,MAAM,EAAEjH;oBAAQ;kBAAE;oBAAAsF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC,EACD/E,SAAS,CAACe,UAAU,EAAE;oBAAEiB,YAAY,EAAEjB;kBAAW,CAAC,CAAC;gBAAA;kBAAA6D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC,GACJ,IACJ;gBACDyB,UAAU,EAAE;kBACXvB,EAAE,EAAE;oBACH,0CAA0C,EAAE;sBAAEwB,MAAM,EAAE;oBAAO,CAAC;oBAC9D,gDAAgD,EAAE;sBAAEA,MAAM,EAAE;oBAAO,CAAC;oBACpE,YAAY,EAAE;sBAAEA,MAAM,EAAE;oBAAO,CAAC;oBAChC,SAAS,EAAE;sBAAEjB,SAAS,EAAE,iBAAiB;sBAAEkB,WAAW,EAAE;oBAAkB,CAAC;oBAC3E,qBAAqB,EAAE;sBAAEjB,MAAM,EAAE,iBAAiB;sBAAEJ,YAAY,EAAE;oBAAM;kBACzE;gBACD;cAAE;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAENtF,OAAA,CAACV,GAAG;cACH0F,SAAS,EAAC,mBAAmB;cAC7BQ,EAAE,EAAE;gBACHC,eAAe,EAAE,oBAAoB;gBACrCO,MAAM,EAAE,iBAAiB;gBACzBN,YAAY,EAAE;cACf,CAAE;cAAAT,QAAA,gBAEFjF,OAAA;gBAAKgF,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAE1E,SAAS,CAAC,aAAa,EAAE;kBAAEgC,YAAY,EAAE;gBAAc,CAAC;cAAC;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtGtF,OAAA;gBAAAiF,QAAA,eACCjF,OAAA;kBACCkH,IAAI,EAAC,OAAO;kBACZ9D,KAAK,EAAEM,uBAAuB,CAAC/C,UAAW;kBAC1C0F,QAAQ,EAAGlD,CAAC,IAAKK,gBAAgB,CAAC,YAAY,EAAEL,CAAC,CAACE,MAAM,CAACD,KAAK,CAAE;kBAChE4B,SAAS,EAAC;gBAAmB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAENtF,OAAA,CAACV,GAAG;cACH0F,SAAS,EAAC,mBAAmB;cAC7BQ,EAAE,EAAE;gBACHC,eAAe,EAAE,oBAAoB;gBACrCO,MAAM,EAAE,iBAAiB;gBACzBN,YAAY,EAAE;cACf,CAAE;cAAAT,QAAA,gBAEFjF,OAAA;gBAAKgF,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAE1E,SAAS,CAAC,MAAM,EAAE;kBAAEgC,YAAY,EAAE;gBAAO,CAAC;cAAC;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxFtF,OAAA;gBAAAiF,QAAA,eACCjF,OAAA;kBAAOgF,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,gBAClDjF,OAAA;oBACCkH,IAAI,EAAC,UAAU;oBACfC,OAAO,EAAEzD,uBAAuB,CAACjB,SAAU;oBAC3C4D,QAAQ,EAAGlD,CAAC,IAAKK,gBAAgB,CAAC,WAAW,EAAEL,CAAC,CAACE,MAAM,CAAC8D,OAAO,CAAE;oBACjEC,IAAI,EAAC;kBAAc;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC,eACFtF,OAAA;oBAAMgF,SAAS,EAAC;kBAAQ;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAENtF,OAAA,CAACV,GAAG;cACH0F,SAAS,EAAC,mBAAmB;cAC7BQ,EAAE,EAAE;gBACHC,eAAe,EAAE,oBAAoB;gBACrCO,MAAM,EAAE,iBAAiB;gBACzBN,YAAY,EAAE;cACf,CAAE;cAAAT,QAAA,gBAEFjF,OAAA;gBAAKgF,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,GAAE1E,SAAS,CAAC,QAAQ,EAAE;kBAAEgC,YAAY,EAAE;gBAAS,CAAC,CAAC,EAAC,GAAC;cAAA;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7FtF,OAAA;gBAAAiF,QAAA,eACCjF,OAAA;kBAAOgF,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,gBAClDjF,OAAA;oBACCkH,IAAI,EAAC,UAAU;oBACfC,OAAO,EAAEzD,uBAAuB,CAAChB,WAAY;oBAC7C2D,QAAQ,EAAGlD,CAAC,IAAKK,gBAAgB,CAAC,aAAa,EAAEL,CAAC,CAACE,MAAM,CAAC8D,OAAO,CAAE;oBACnEC,IAAI,EAAC;oBACL;kBAAA;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eACFtF,OAAA;oBAAMgF,SAAS,EAAC;kBAAQ;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAENtF,OAAA,CAACV,GAAG;YACHkG,EAAE,EAAE;cACHC,eAAe,EAAE,SAAS;cAC1BC,YAAY,EAAE,6BAA6B;cAC3CM,MAAM,EAAE,MAAM;cACdL,OAAO,EAAE,KAAK;cACdC,YAAY,EAAE;YACf,CAAE;YAAAX,QAAA,gBAEFjF,OAAA,CAACT,UAAU;cAACiG,EAAE,EAAE;gBAAEK,UAAU,EAAE,KAAK;gBAAEC,aAAa,EAAE,KAAK;gBAAEC,SAAS,EAAE;cAAO,CAAE;cAAAd,QAAA,EAAE1E,SAAS,CAAC,WAAW,EAAE;gBAAEgC,YAAY,EAAE;cAAY,CAAC;YAAC;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAEpJtF,OAAA,CAACV,GAAG;cACH0F,SAAS,EAAC,mBAAmB;cAC7BQ,EAAE,EAAE;gBAAEG,OAAO,EAAE,cAAc;gBAAEK,MAAM,EAAE;cAAkB,CAAE;cAAAf,QAAA,eAE3DjF,OAAA,CAACR,SAAS;gBACTyG,OAAO,EAAC,UAAU;gBAClBV,IAAI,EAAC,OAAO;gBACZW,WAAW,EAAE3F,SAAS,CAAC,iBAAiB,EAAE;kBAAEgC,YAAY,EAAE;gBAAkB,CAAC,CAAE;gBAC/EyC,SAAS,EAAC,qBAAqB;gBAC/B5B,KAAK,EAAEM,uBAAuB,CAAClB,QAAS;gBACxC2D,KAAK,EAAE;kBAAEC,KAAK,EAAE;gBAAO,CAAE;gBACzBC,QAAQ,EAAE5C,oBAAqB;gBAC/B6C,KAAK,EAAEC,OAAO,CAAC/E,aAAa,CAAE,CAAC;gBAAA;gBAC/BgF,UAAU,EACThF,aAAa,gBACZxB,OAAA;kBAAMmG,KAAK,EAAE;oBAAEM,OAAO,EAAE,MAAM;oBAAEC,QAAQ,EAAE,MAAM;oBAAEC,UAAU,EAAE;kBAAS,CAAE;kBAAA1B,QAAA,gBACxEjF,OAAA;oBACCmG,KAAK,EAAE;sBAAES,WAAW,EAAE;oBAAM,CAAE;oBAC9BC,uBAAuB,EAAE;sBAAEC,MAAM,EAAEjH;oBAAQ;kBAAE;oBAAAsF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC,EACD/E,SAAS,CAACiB,aAAa,EAAE;oBAAEe,YAAY,EAAEf;kBAAc,CAAC,CAAC;gBAAA;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,GACJ,IACJ;gBACDyB,UAAU,EAAE;kBACXM,YAAY,EAAE,EAAE;kBAChB7B,EAAE,EAAE;oBACH,0CAA0C,EAAE;sBAAEwB,MAAM,EAAE;oBAAO,CAAC;oBAC9D,gDAAgD,EAAE;sBAAEA,MAAM,EAAE;oBAAO,CAAC;oBACpE,YAAY,EAAE;sBAAEA,MAAM,EAAE;oBAAO,CAAC;oBAChC,SAAS,EAAE;sBAAEjB,SAAS,EAAE,iBAAiB;sBAAEkB,WAAW,EAAE;oBAAkB,CAAC;oBAC3E,qBAAqB,EAAE;sBAAEjB,MAAM,EAAE,iBAAiB;sBAAEJ,YAAY,EAAE;oBAAM;kBACzE;gBACD;cAAE;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAENtF,OAAA,CAACV,GAAG;cACH0F,SAAS,EAAC,mBAAmB;cAC7BQ,EAAE,EAAE;gBACHC,eAAe,EAAE,oBAAoB;gBACrCO,MAAM,EAAE,iBAAiB;gBACzBN,YAAY,EAAE;cACf,CAAE;cAAAT,QAAA,gBAEFjF,OAAA;gBAAKgF,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAE1E,SAAS,CAAC,gBAAgB,EAAE;kBAAEgC,YAAY,EAAE;gBAAiB,CAAC;cAAC;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5GtF,OAAA;gBAAAiF,QAAA,eACCjF,OAAA;kBACCkH,IAAI,EAAC,OAAO;kBACZ9D,KAAK,EAAEM,uBAAuB,CAACf,aAAc;kBAC7C0D,QAAQ,EAAGlD,CAAC,IAAKK,gBAAgB,CAAC,eAAe,EAAEL,CAAC,CAACE,MAAM,CAACD,KAAK,CAAE;kBACnE4B,SAAS,EAAC;gBAAmB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAENtF,OAAA,CAACV,GAAG;cACH0F,SAAS,EAAC,mBAAmB;cAC7BQ,EAAE,EAAE;gBACHC,eAAe,EAAE,oBAAoB;gBACrCO,MAAM,EAAE,iBAAiB;gBACzBN,YAAY,EAAE;cACf,CAAE;cAAAT,QAAA,gBAEFjF,OAAA;gBAAKgF,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAE1E,SAAS,CAAC,MAAM,EAAE;kBAAEgC,YAAY,EAAE;gBAAO,CAAC;cAAC;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxFtF,OAAA;gBAAAiF,QAAA,eACCjF,OAAA;kBAAOgF,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAChCjF,OAAA;oBACCkH,IAAI,EAAC,UAAU;oBACfC,OAAO,EAAEzD,uBAAuB,CAACd,YAAa;oBAC9CyD,QAAQ,EAAGlD,CAAC,IAAKK,gBAAgB,CAAC,cAAc,EAAEL,CAAC,CAACE,MAAM,CAAC8D,OAAO,CAAE;oBACpEC,IAAI,EAAC;kBAAc;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC,eACFtF,OAAA;oBAAMgF,SAAS,EAAC;kBAAQ;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAENtF,OAAA,CAACV,GAAG;cACH0F,SAAS,EAAC,mBAAmB;cAC7BQ,EAAE,EAAE;gBACHC,eAAe,EAAE,oBAAoB;gBACrCO,MAAM,EAAE,iBAAiB;gBACzBN,YAAY,EAAE;cACf,CAAE;cAAAT,QAAA,gBAEFjF,OAAA;gBAAKgF,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,GAAE1E,SAAS,CAAC,QAAQ,EAAE;kBAAEgC,YAAY,EAAE;gBAAS,CAAC,CAAC,EAAC,GAAC;cAAA;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7FtF,OAAA;gBAAAiF,QAAA,eACCjF,OAAA;kBAAOgF,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,gBAClDjF,OAAA;oBACCkH,IAAI,EAAC,UAAU;oBACfC,OAAO,EAAEzD,uBAAuB,CAACb,cAAe;oBAChDwD,QAAQ,EAAGlD,CAAC,IAAKK,gBAAgB,CAAC,gBAAgB,EAAEL,CAAC,CAACE,MAAM,CAAC8D,OAAO,CAAE;oBACtEC,IAAI,EAAC;kBAAc;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC,eACFtF,OAAA;oBAAMgF,SAAS,EAAC;kBAAQ;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACNtF,OAAA;QAAKgF,SAAS,EAAC,oBAAoB;QAAAC,QAAA,eAClCjF,OAAA,CAACN,MAAM;UACNuG,OAAO,EAAC,WAAW;UACnBf,OAAO,EAAER,kBAAmB;UAC5BM,SAAS,EAAE,aAAatD,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;UACvD4F,QAAQ,EAAE5F,UAAW;UAAAuD,QAAA,EAEpB1E,SAAS,CAAC,OAAO,EAAE;YAAEgC,YAAY,EAAE;UAAQ,CAAC;QAAC;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAET,CAAC;AAACjF,EAAA,CAlaIJ,aAAa;EAAA,QACOZ,cAAc,EAelCO,cAAc;AAAA;AAAA2H,EAAA,GAhBdtH,aAAa;AAoanB,eAAeA,aAAa;AAAC,IAAAsH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}