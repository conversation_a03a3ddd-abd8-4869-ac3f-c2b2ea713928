{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideDesign\\\\Overlay.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { Box, IconButton, Button } from \"@mui/material\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\nimport { useTranslation } from 'react-i18next';\nimport useDrawerStore from \"../../store/drawerStore\";\nimport \"./Canvas.module.css\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst OverlaySettings = ({\n  selectedTemplate,\n  onStatusChange,\n  setOverLays,\n  anchorEl,\n  onBack,\n  onClose,\n  setOverlaySettings,\n  setDesignPopup,\n  setMenuPopup\n}) => {\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  const [isOpen, setIsOpen] = useState(true);\n  const {\n    setOverlayEnabled,\n    overlayEnabled,\n    selectedTemplateTour,\n    setIsUnSavedChanges\n  } = useDrawerStore(state => state);\n  const {\n    pageinteraction,\n    setPageInteraction\n  } = useDrawerStore(state => state);\n\n  // Initialize local state from store values\n  const [interaction, setInteraction] = useState(pageinteraction);\n  const [status, setStatus] = useState(overlayEnabled);\n\n  // Debug: Log the store values\n  console.log('Overlay Component - Store values:', {\n    overlayEnabled,\n    pageinteraction\n  });\n  console.log('Overlay Component - Local state:', {\n    status,\n    interaction\n  });\n\n  // Keep local state in sync with store when store values change\n  useEffect(() => {\n    console.log('Overlay Component - useEffect triggered:', {\n      overlayEnabled,\n      pageinteraction\n    });\n    setStatus(overlayEnabled);\n    setInteraction(pageinteraction);\n  }, [overlayEnabled, pageinteraction]);\n  const handleStatusChange = event => {\n    const newStatus = event.target.checked;\n    setStatus(newStatus);\n\n    // Implement mutual exclusivity: when overlay changes, page interaction must be opposite\n    if (newStatus === false) {\n      // When overlay is disabled, automatically enable page interaction\n      setInteraction(true);\n    } else {\n      // When overlay is enabled, automatically disable page interaction\n      setInteraction(false);\n    }\n  };\n  const handleApplyChanges = () => {\n    // Create a batch update function to record a single history entry\n    const batchUpdate = useDrawerStore.getState().batchUpdate;\n\n    // Use the batch update function to record a single history entry\n    batchUpdate(() => {\n      // Apply the changes\n      setOverlayEnabled(status);\n      setPageInteraction(interaction);\n    }, 'OVERLAY_BATCH_UPDATE', `Updated overlay settings`);\n\n    // Update UI state\n    setIsOpen(false);\n    setOverlaySettings(false);\n    setMenuPopup(true);\n    setIsUnSavedChanges(true);\n  };\n  const handleInteractionChange = event => {\n    const newInteraction = event.target.checked;\n    setInteraction(newInteraction);\n\n    // Implement asymmetric mutual exclusivity: only disable overlay when page interaction is enabled\n    if (newInteraction === true) {\n      // When page interaction is enabled, automatically disable overlay\n      setStatus(false);\n    }\n    // When page interaction is disabled, do NOT automatically enable overlay\n    // This allows both options to be disabled simultaneously\n  };\n  const handleClose = () => {\n    setIsOpen(false);\n    setOverlaySettings(false);\n    setMenuPopup(true);\n  };\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    id: \"qadpt-designpopup\",\n    className: \"qadpt-designpopup\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadpt-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-design-header\",\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          \"aria-label\": translate(\"back\"),\n          onClick: onBack,\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIosNewOutlinedIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-title\",\n          children: [\" \", selectedTemplate !== \"Banner\" || selectedTemplateTour !== \"Banner\" ? translate(\"Overlay\") : translate(\"Shadow\")]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"small\",\n          \"aria-label\": translate(\"close\"),\n          onClick: onClose,\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 6\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-controls\"\n        //style={{ opacity: \"0.5\", height: \"60px\" }}\n        ,\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          className: \"qadpt-control-box\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-control-label\",\n            children: selectedTemplate === \"Banner\" || selectedTemplateTour === \"Banner\" ? translate(\"Enable Shadow\") : translate(\"Enable Overlay\")\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"toggle-switch \",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: status,\n                onChange: handleStatusChange,\n                name: \"toggleSwitch\",\n                disabled: selectedTemplate === \"Tour\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"slider\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 9\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 7\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 6\n        }, this), selectedTemplate !== \"Banner\" && selectedTemplateTour !== \"Banner\" && !status && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-control-label\",\n              children: translate(\"Interact with Page\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"toggle-switch qadpt-toggle-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: interaction,\n                  onChange: handleInteractionChange,\n                  name: \"toggleSwitch\",\n                  disabled: selectedTemplate === \"Tour\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 11\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"slider\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 11\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 10\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 8\n          }, this)\n        }, void 0, false)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-drawerFooter\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: handleApplyChanges,\n          className: `qadpt-btn`,\n          children: translate(\"Apply\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 5\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 4\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 105,\n    columnNumber: 3\n  }, this);\n};\n_s(OverlaySettings, \"OG7DQR/rMCMnogw6auC46du2KOM=\", false, function () {\n  return [useTranslation, useDrawerStore, useDrawerStore];\n});\n_c = OverlaySettings;\nexport default OverlaySettings;\nvar _c;\n$RefreshReg$(_c, \"OverlaySettings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "IconButton", "<PERSON><PERSON>", "CloseIcon", "ArrowBackIosNewOutlinedIcon", "useTranslation", "useDrawerStore", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "OverlaySettings", "selectedTemplate", "onStatusChange", "setOverLays", "anchorEl", "onBack", "onClose", "setOverlaySettings", "setDesignPopup", "setMenuPopup", "_s", "t", "translate", "isOpen", "setIsOpen", "setOverlayEnabled", "overlayEnabled", "selectedTemplateTour", "setIsUnSavedChanges", "state", "pageinteraction", "setPageInteraction", "interaction", "setInteraction", "status", "setStatus", "console", "log", "handleStatusChange", "event", "newStatus", "target", "checked", "handleApplyChanges", "batchUpdate", "getState", "handleInteractionChange", "newInteraction", "handleClose", "id", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "type", "onChange", "name", "disabled", "variant", "_c", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/guideDesign/Overlay.tsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { Box, Typography, ToggleButton, ToggleButtonGroup, IconButton, FormControlLabel, Switch, Button } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\r\nimport { useTranslation } from 'react-i18next';\r\nimport useDrawerStore, { DrawerState } from \"../../store/drawerStore\";\r\nimport \"./Canvas.module.css\";\r\n\r\ninterface OverlaySettingsProps {\r\n\tselectedTemplate: string;\r\n\tonStatusChange: (status: boolean) => void;\r\n\tsetOverLays: (status: boolean) => void;\r\n\tanchorEl: HTMLElement | null;\r\n\tsetDesignPopup: (status: boolean) => void;\r\n}\r\n\r\nconst OverlaySettings = ({\r\n\tselectedTemplate,\r\n\tonStatusChange,\r\n\tsetOverLays,\r\n\tanchorEl,\r\n\tonBack,\r\n\tonClose,\r\n\tsetOverlaySettings,\r\n\tsetDesignPopup,\r\n\tsetMenuPopup\r\n}: any) => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst [isOpen, setIsOpen] = useState(true);\r\n\tconst { setOverlayEnabled, overlayEnabled, selectedTemplateTour, setIsUnSavedChanges } = useDrawerStore((state) => state);\r\n\tconst { pageinteraction, setPageInteraction } = useDrawerStore((state: DrawerState) => state);\r\n\r\n\t// Initialize local state from store values\r\n\tconst [interaction, setInteraction] = useState<boolean>(pageinteraction);\r\n\tconst [status, setStatus] = useState(overlayEnabled);\r\n\r\n\t// Debug: Log the store values\r\n\tconsole.log('Overlay Component - Store values:', { overlayEnabled, pageinteraction });\r\n\tconsole.log('Overlay Component - Local state:', { status, interaction });\r\n\r\n\t// Keep local state in sync with store when store values change\r\n\tuseEffect(() => {\r\n\t\tconsole.log('Overlay Component - useEffect triggered:', { overlayEnabled, pageinteraction });\r\n\t\tsetStatus(overlayEnabled);\r\n\t\tsetInteraction(pageinteraction);\r\n\t}, [overlayEnabled, pageinteraction]);\r\n\r\n\tconst handleStatusChange = (event: any) => {\r\n\t\tconst newStatus = event.target.checked;\r\n\t\tsetStatus(newStatus);\r\n\r\n\t\t// Implement mutual exclusivity: when overlay changes, page interaction must be opposite\r\n\t\tif (newStatus === false) {\r\n\t\t\t// When overlay is disabled, automatically enable page interaction\r\n\t\t\tsetInteraction(true);\r\n\t\t} else {\r\n\t\t\t// When overlay is enabled, automatically disable page interaction\r\n\t\t\tsetInteraction(false);\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleApplyChanges = () => {\r\n\t\t// Create a batch update function to record a single history entry\r\n\t\tconst batchUpdate = useDrawerStore.getState().batchUpdate;\r\n\r\n\t\t// Use the batch update function to record a single history entry\r\n\t\tbatchUpdate(\r\n\t\t\t() => {\r\n\t\t\t\t// Apply the changes\r\n\t\t\t\tsetOverlayEnabled(status);\r\n\t\t\t\tsetPageInteraction(interaction);\r\n\t\t\t},\r\n\t\t\t'OVERLAY_BATCH_UPDATE',\r\n\t\t\t`Updated overlay settings`\r\n\t\t);\r\n\r\n\t\t// Update UI state\r\n\t\tsetIsOpen(false);\r\n\t\tsetOverlaySettings(false);\r\n\t\tsetMenuPopup(true);\r\n\t\tsetIsUnSavedChanges(true);\r\n\t};\r\n\r\n\tconst handleInteractionChange = (event: any) => {\r\n\t\tconst newInteraction = event.target.checked;\r\n\t\tsetInteraction(newInteraction);\r\n\r\n\t\t// Implement asymmetric mutual exclusivity: only disable overlay when page interaction is enabled\r\n\t\tif (newInteraction === true) {\r\n\t\t\t// When page interaction is enabled, automatically disable overlay\r\n\t\t\tsetStatus(false);\r\n\t\t}\r\n\t\t// When page interaction is disabled, do NOT automatically enable overlay\r\n\t\t// This allows both options to be disabled simultaneously\r\n\t};\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetIsOpen(false);\r\n\t\tsetOverlaySettings(false);\r\n\t\tsetMenuPopup(true);\r\n\t};\r\n\tif (!isOpen) return null;\r\n\r\n\treturn (\r\n\t\t<div\r\n\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\tclassName=\"qadpt-designpopup\"\r\n\t\t>\r\n\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\taria-label={translate(\"back\")}\r\n\t\t\t\t\t\tonClick={onBack}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<ArrowBackIosNewOutlinedIcon />\r\n\t\t\t\t\t</IconButton>\r\n\r\n\t\t\t\t\t<div className=\"qadpt-title\"> {(selectedTemplate !== \"Banner\" || selectedTemplateTour !== \"Banner\") ? translate(\"Overlay\") : translate(\"Shadow\")}</div>\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\taria-label={translate(\"close\")}\r\n\t\t\t\t\t\tonClick={onClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t{/* Status Section */}\r\n\t\t\t\t<div\r\n\t\t\t\t\tclassName=\"qadpt-controls\"\r\n\t\t\t\t//style={{ opacity: \"0.5\", height: \"60px\" }}\r\n\t\t\t\t>\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{selectedTemplate === \"Banner\" || selectedTemplateTour === \"Banner\" ? translate(\"Enable Shadow\") : translate(\"Enable Overlay\")}\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<label className=\"toggle-switch \">\r\n\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\ttype=\"checkbox\"\r\n\t\t\t\t\t\t\t\t\tchecked={status}\r\n\t\t\t\t\t\t\t\t\tonChange={handleStatusChange}\r\n\t\t\t\t\t\t\t\t\tname=\"toggleSwitch\"\r\n\t\t\t\t\t\t\t\t\tdisabled={selectedTemplate === \"Tour\"}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t<span className=\"slider\"></span>\r\n\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t{/* Enable Shadow or Enable Overlay */}\r\n\t\t\t\t\t{/* <div\r\n\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\tjustifyContent: \"space-between\",\r\n\t\t\t\t\t\t\tpadding: \"5px\",\r\n\t\t\t\t\t\t\tborderRadius: \"12px\",\r\n\t\t\t\t\t\t\tbackgroundColor: \"var(--back-light-color)\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<div className=\"qadpt-label\">{selectedTemplate === \"Banner\" || selectedTemplateTour === \"Banner\" ? \"Enable Shadow\" : \"Enable Overlay\"}</div>\r\n\t\t\t\t\t\t<label className=\"toggle-switch \">\r\n    <input\r\n        type=\"checkbox\"\r\n        checked={status}\r\n        onChange={handleStatusChange}\r\n        name=\"toggleSwitch\"\r\n    />\r\n    <span className=\"slider\"></span>\r\n</label>\r\n\r\n\t\t\t\t\t</div> */}\r\n\r\n\t\t\t\t\t{(selectedTemplate !== \"Banner\" && selectedTemplateTour !== \"Banner\") && !status && (\r\n\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t\t{/* Interact with Page */}\r\n\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{translate(\"Interact with Page\")}\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t<label className=\"toggle-switch qadpt-toggle-group\">\r\n\t\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\t\ttype=\"checkbox\"\r\n\t\t\t\t\t\t\t\t\t\t\tchecked={interaction}\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={handleInteractionChange}\r\n\t\t\t\t\t\t\t\t\t\t\tname=\"toggleSwitch\"\r\n\t\t\t\t\t\t\t\t\t\t\tdisabled={selectedTemplate === \"Tour\"}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t<span className=\"slider\"></span>\r\n\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</>\r\n\t\t\t\t\t)}\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t<div className=\"qadpt-drawerFooter\">\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\tonClick={handleApplyChanges}\r\n\t\t\t\t\t\tclassName={`qadpt-btn`}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{translate(\"Apply\")}\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t);\r\n};\r\n\r\nexport default OverlaySettings;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,GAAG,EAA+CC,UAAU,EAA4BC,MAAM,QAAQ,eAAe;AAC9H,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,2BAA2B,MAAM,6CAA6C;AACrF,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,cAAc,MAAuB,yBAAyB;AACrE,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAU7B,MAAMC,eAAe,GAAGA,CAAC;EACxBC,gBAAgB;EAChBC,cAAc;EACdC,WAAW;EACXC,QAAQ;EACRC,MAAM;EACNC,OAAO;EACPC,kBAAkB;EAClBC,cAAc;EACdC;AACI,CAAC,KAAK;EAAAC,EAAA;EACV,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGlB,cAAc,CAAC,CAAC;EACzC,MAAM,CAACmB,MAAM,EAAEC,SAAS,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM;IAAE4B,iBAAiB;IAAEC,cAAc;IAAEC,oBAAoB;IAAEC;EAAoB,CAAC,GAAGvB,cAAc,CAAEwB,KAAK,IAAKA,KAAK,CAAC;EACzH,MAAM;IAAEC,eAAe;IAAEC;EAAmB,CAAC,GAAG1B,cAAc,CAAEwB,KAAkB,IAAKA,KAAK,CAAC;;EAE7F;EACA,MAAM,CAACG,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAUiC,eAAe,CAAC;EACxE,MAAM,CAACI,MAAM,EAAEC,SAAS,CAAC,GAAGtC,QAAQ,CAAC6B,cAAc,CAAC;;EAEpD;EACAU,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE;IAAEX,cAAc;IAAEI;EAAgB,CAAC,CAAC;EACrFM,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE;IAAEH,MAAM;IAAEF;EAAY,CAAC,CAAC;;EAExE;EACAlC,SAAS,CAAC,MAAM;IACfsC,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE;MAAEX,cAAc;MAAEI;IAAgB,CAAC,CAAC;IAC5FK,SAAS,CAACT,cAAc,CAAC;IACzBO,cAAc,CAACH,eAAe,CAAC;EAChC,CAAC,EAAE,CAACJ,cAAc,EAAEI,eAAe,CAAC,CAAC;EAErC,MAAMQ,kBAAkB,GAAIC,KAAU,IAAK;IAC1C,MAAMC,SAAS,GAAGD,KAAK,CAACE,MAAM,CAACC,OAAO;IACtCP,SAAS,CAACK,SAAS,CAAC;;IAEpB;IACA,IAAIA,SAAS,KAAK,KAAK,EAAE;MACxB;MACAP,cAAc,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM;MACN;MACAA,cAAc,CAAC,KAAK,CAAC;IACtB;EACD,CAAC;EAED,MAAMU,kBAAkB,GAAGA,CAAA,KAAM;IAChC;IACA,MAAMC,WAAW,GAAGvC,cAAc,CAACwC,QAAQ,CAAC,CAAC,CAACD,WAAW;;IAEzD;IACAA,WAAW,CACV,MAAM;MACL;MACAnB,iBAAiB,CAACS,MAAM,CAAC;MACzBH,kBAAkB,CAACC,WAAW,CAAC;IAChC,CAAC,EACD,sBAAsB,EACtB,0BACD,CAAC;;IAED;IACAR,SAAS,CAAC,KAAK,CAAC;IAChBP,kBAAkB,CAAC,KAAK,CAAC;IACzBE,YAAY,CAAC,IAAI,CAAC;IAClBS,mBAAmB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMkB,uBAAuB,GAAIP,KAAU,IAAK;IAC/C,MAAMQ,cAAc,GAAGR,KAAK,CAACE,MAAM,CAACC,OAAO;IAC3CT,cAAc,CAACc,cAAc,CAAC;;IAE9B;IACA,IAAIA,cAAc,KAAK,IAAI,EAAE;MAC5B;MACAZ,SAAS,CAAC,KAAK,CAAC;IACjB;IACA;IACA;EACD,CAAC;EAED,MAAMa,WAAW,GAAGA,CAAA,KAAM;IACzBxB,SAAS,CAAC,KAAK,CAAC;IAChBP,kBAAkB,CAAC,KAAK,CAAC;IACzBE,YAAY,CAAC,IAAI,CAAC;EACnB,CAAC;EACD,IAAI,CAACI,MAAM,EAAE,OAAO,IAAI;EAExB,oBACChB,OAAA;IACC0C,EAAE,EAAC,mBAAmB;IACtBC,SAAS,EAAC,mBAAmB;IAAAC,QAAA,eAE7B5C,OAAA;MAAK2C,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC7B5C,OAAA;QAAK2C,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBACnC5C,OAAA,CAACP,UAAU;UACV,cAAYsB,SAAS,CAAC,MAAM,CAAE;UAC9B8B,OAAO,EAAErC,MAAO;UAAAoC,QAAA,eAEhB5C,OAAA,CAACJ,2BAA2B;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eAEbjD,OAAA;UAAK2C,SAAS,EAAC,aAAa;UAAAC,QAAA,GAAC,GAAC,EAAExC,gBAAgB,KAAK,QAAQ,IAAIgB,oBAAoB,KAAK,QAAQ,GAAIL,SAAS,CAAC,SAAS,CAAC,GAAGA,SAAS,CAAC,QAAQ,CAAC;QAAA;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvJjD,OAAA,CAACP,UAAU;UACVyD,IAAI,EAAC,OAAO;UACZ,cAAYnC,SAAS,CAAC,OAAO,CAAE;UAC/B8B,OAAO,EAAEpC,OAAQ;UAAAmC,QAAA,eAEjB5C,OAAA,CAACL,SAAS;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eAGNjD,OAAA;QACC2C,SAAS,EAAC;QACX;QAAA;QAAAC,QAAA,gBAEC5C,OAAA,CAACR,GAAG;UAACmD,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBACjC5C,OAAA;YACC2C,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAG9BxC,gBAAgB,KAAK,QAAQ,IAAIgB,oBAAoB,KAAK,QAAQ,GAAGL,SAAS,CAAC,eAAe,CAAC,GAAGA,SAAS,CAAC,gBAAgB;UAAC;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1H,CAAC,eACNjD,OAAA;YAAA4C,QAAA,eACC5C,OAAA;cAAO2C,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAChC5C,OAAA;gBACCmD,IAAI,EAAC,UAAU;gBACfhB,OAAO,EAAER,MAAO;gBAChByB,QAAQ,EAAErB,kBAAmB;gBAC7BsB,IAAI,EAAC,cAAc;gBACnBC,QAAQ,EAAElD,gBAAgB,KAAK;cAAO;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACFjD,OAAA;gBAAM2C,SAAS,EAAC;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEF,CAAC,EAyBJ7C,gBAAgB,KAAK,QAAQ,IAAIgB,oBAAoB,KAAK,QAAQ,IAAK,CAACO,MAAM,iBAC/E3B,OAAA,CAAAE,SAAA;UAAA0C,QAAA,eACC5C,OAAA,CAACR,GAAG;YAACmD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAEjC5C,OAAA;cACC2C,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAE9B7B,SAAS,CAAC,oBAAoB;YAAC;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACNjD,OAAA;cAAA4C,QAAA,eACC5C,OAAA;gBAAO2C,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAClD5C,OAAA;kBACCmD,IAAI,EAAC,UAAU;kBACfhB,OAAO,EAAEV,WAAY;kBACrB2B,QAAQ,EAAEb,uBAAwB;kBAClCc,IAAI,EAAC,cAAc;kBACnBC,QAAQ,EAAElD,gBAAgB,KAAK;gBAAO;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACFjD,OAAA;kBAAM2C,SAAS,EAAC;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC,gBACL,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAENjD,OAAA;QAAK2C,SAAS,EAAC,oBAAoB;QAAAC,QAAA,eAClC5C,OAAA,CAACN,MAAM;UACN6D,OAAO,EAAC,WAAW;UACnBV,OAAO,EAAET,kBAAmB;UAC5BO,SAAS,EAAE,WAAY;UAAAC,QAAA,EAEtB7B,SAAS,CAAC,OAAO;QAAC;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAER,CAAC;AAACpC,EAAA,CAvMIV,eAAe;EAAA,QAWKN,cAAc,EAEkDC,cAAc,EACvDA,cAAc;AAAA;AAAA0D,EAAA,GAdzDrD,eAAe;AAyMrB,eAAeA,eAAe;AAAC,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}