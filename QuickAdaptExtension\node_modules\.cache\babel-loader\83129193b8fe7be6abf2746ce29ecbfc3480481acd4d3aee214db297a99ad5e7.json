{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\hotspot\\\\HotspotSettings.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { Box, Typography, TextField, IconButton, Button, FormControl, Select, MenuItem } from \"@mui/material\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport useDrawerStore from \"../../store/drawerStore\";\nimport { InfoFilled, QuestionFill, Reselect, Solid } from \"../../assets/icons/icons\";\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\nimport { useTranslation } from 'react-i18next';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HotspotSettings = ({\n  currentGuide,\n  onBack,\n  onClose\n}) => {\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    setHotspotPopup,\n    setElementSelected,\n    updatehotspots,\n    setDesignPopup,\n    toolTipGuideMetaData,\n    setOpenTooltip,\n    openTooltip,\n    currentStep,\n    setTooltipPositionByXpath,\n    updateCanvasInTooltip,\n    updateTooltipBtnContainer,\n    updateTooltipImageContainer,\n    pulseAnimationsH,\n    setPulseAnimationsH,\n    setIsUnSavedChanges\n  } = useDrawerStore(state => state);\n  const [hotSpotProperties, setHotSpotProperties] = useState(() => {\n    var _toolTipGuideMetaData;\n    // Get the current step's hotspot properties\n    const currentStepIndex = currentStep - 1;\n    const currentStepHotspots = (_toolTipGuideMetaData = toolTipGuideMetaData[currentStepIndex]) === null || _toolTipGuideMetaData === void 0 ? void 0 : _toolTipGuideMetaData.hotspots;\n\n    // Use the current step's hotspot properties if available, otherwise use default values\n    const initialHotspotProperties = currentStepHotspots || {\n      XPosition: \"4\",\n      YPosition: \"4\",\n      Type: \"Question\",\n      Color: \"yellow\",\n      Size: 16,\n      PulseAnimation: pulseAnimationsH,\n      stopAnimationUponInteraction: true,\n      ShowUpon: \"Hovering Hotspot\",\n      ShowByDefault: false\n    };\n    return initialHotspotProperties;\n  });\n\n  // Update hotspot properties when currentStep or toolTipGuideMetaData changes\n  useEffect(() => {\n    var _toolTipGuideMetaData2;\n    const currentStepIndex = currentStep - 1;\n    const currentStepHotspots = (_toolTipGuideMetaData2 = toolTipGuideMetaData[currentStepIndex]) === null || _toolTipGuideMetaData2 === void 0 ? void 0 : _toolTipGuideMetaData2.hotspots;\n    if (currentStepHotspots) {\n      // Update local state with the current step's hotspot properties\n      setHotSpotProperties({\n        XPosition: currentStepHotspots.XPosition || \"4\",\n        YPosition: currentStepHotspots.YPosition || \"4\",\n        Type: currentStepHotspots.Type || \"Question\",\n        Color: currentStepHotspots.Color || \"yellow\",\n        Size: currentStepHotspots.Size || 16,\n        PulseAnimation: currentStepHotspots.PulseAnimation !== undefined ? currentStepHotspots.PulseAnimation : pulseAnimationsH,\n        stopAnimationUponInteraction: currentStepHotspots.stopAnimationUponInteraction !== undefined ? currentStepHotspots.stopAnimationUponInteraction : true,\n        ShowUpon: currentStepHotspots.ShowUpon || \"Hovering Hotspot\",\n        ShowByDefault: currentStepHotspots.ShowByDefault || false\n      });\n    } else {\n      // If no hotspot data exists for this step, use default values\n      setHotSpotProperties({\n        XPosition: \"4\",\n        YPosition: \"4\",\n        Type: \"Question\",\n        Color: \"yellow\",\n        Size: 16,\n        PulseAnimation: pulseAnimationsH,\n        stopAnimationUponInteraction: true,\n        ShowUpon: \"Hovering Hotspot\",\n        ShowByDefault: false\n      });\n    }\n  }, [currentStep, toolTipGuideMetaData, pulseAnimationsH]);\n  const handleClose = () => {\n    setHotspotPopup(false);\n  };\n  const handledesignclose = () => {\n    setDesignPopup(false);\n  };\n  const handleSizeChange = value => {\n    const sizeInPx = 16 + (value - 1) * 4;\n    onPropertyChange(\"Size\", sizeInPx);\n  };\n  const onReselectElement = () => {\n    // setHotSpotProperties({\n    // \tXPosition: \"4\",\n    // \tYPosition: \"4\",\n    // \tType: \"Question\",\n    // \tColor: \"y4ellow\",\n    // \tSize: 1,\n    // \tPulseAnimation: pulseAnimationsH,\n    // \tstopAnimationUponInteraction: true,\n    // \tShowUpon: \"Hovering Hotspot\",\n    // \tShowByDefault: false,\n    // });\n    setElementSelected(false);\n    handledesignclose();\n    //updatehotspots(HOTSPOT_DEFAULT_VALUE);\n    // updateCanvasInTooltip(CANVAS_DEFAULT_VALUE);\n    //  updateTooltipBtnContainer(BUTTON_CONT_DEF_VALUE_1);\n    //  updateTooltipImageContainer(IMG_CONT_DEF_VALUE);\n    const existingHotspot = document.getElementById(\"hotspotBlinkCreation\");\n    const existingTooltip = document.getElementById(\"Tooltip-unique\");\n    const allElementsWithOutline = document.querySelectorAll(\"[style*='outline']\");\n    allElementsWithOutline.forEach(element => {\n      element.style.outline = \"\"; // Reset the outline (border) color\n    });\n    if (existingHotspot) {\n      existingHotspot.remove();\n    }\n    // if (existingTooltip)\n    // {\n    //   existingTooltip.remove();\n    // }\n    setHotspotPopup(false);\n    setIsUnSavedChanges(true);\n  };\n  const onPropertyChange = (key, value) => {\n    setHotSpotProperties(prevState => ({\n      ...prevState,\n      [key]: value\n    }));\n  };\n  const handleApplyChanges = () => {\n    updatehotspots(hotSpotProperties);\n    handleClose();\n    setHotspotPopup(false);\n    setIsUnSavedChanges(true);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    id: \"qadpt-designpopup\",\n    className: \"qadpt-designpopup\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadpt-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-design-header\",\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          \"aria-label\": translate(\"back\"),\n          onClick: onBack,\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIosNewOutlinedIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-title\",\n          children: translate(\"Hotspot\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"small\",\n          \"aria-label\": translate(\"Close\"),\n          onClick: onClose,\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 7\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 6\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-canblock\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-controls\",\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            sx: {\n              cursor: \"pointer\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              className: \"qadpt-control-label\",\n              children: translate(\"Reselect Element\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              onClick: onReselectElement,\n              dangerouslySetInnerHTML: {\n                __html: Reselect\n              },\n              style: {\n                padding: \"5px\",\n                marginRight: \"10px\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 9\n            }, this), \" \"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-position-grid\",\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              className: \"qadpt-ctrl-title\",\n              children: translate(\"Position within Element\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-controls\",\n              style: {\n                padding: \"0px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                className: \"qadpt-control-box\",\n                sx: {\n                  padding: \"0 !important\",\n                  marginBottom: \"0 !important\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  className: \"qadpt-control-label\",\n                  sx: {\n                    fontSize: \"12px !important\",\n                    paddingLeft: \"0 !important\",\n                    margin: \"3px\"\n                  },\n                  children: [\"X \", translate(\"Axis Offset\")]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 11\n                }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                  variant: \"outlined\",\n                  value: hotSpotProperties.XPosition,\n                  size: \"small\",\n                  className: \"qadpt-control-input\",\n                  onChange: e => onPropertyChange(\"XPosition\", e.target.value),\n                  InputProps: {\n                    endAdornment: \"%\",\n                    sx: {\n                      \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"& fieldset\": {\n                        border: \"none\"\n                      }\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 11\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                className: \"qadpt-control-box\",\n                sx: {\n                  padding: \"0 !important\",\n                  marginBottom: \"0 !important\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  className: \"qadpt-control-label\",\n                  sx: {\n                    fontSize: \"12px !important\",\n                    paddingLeft: \"0 !important\",\n                    margin: \"3px\"\n                  },\n                  children: [\"Y \", translate(\"Axis Offset\")]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 11\n                }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                  variant: \"outlined\",\n                  value: hotSpotProperties.YPosition,\n                  size: \"small\",\n                  className: \"qadpt-control-input\",\n                  onChange: e => onPropertyChange(\"YPosition\", e.target.value),\n                  InputProps: {\n                    endAdornment: \"%\",\n                    sx: {\n                      \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"& fieldset\": {\n                        border: \"none\"\n                      }\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 11\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            id: \"qadpt-designpopup\",\n            className: \"qadpt-control-box\",\n            sx: {\n              flexDirection: \"column\",\n              height: \"auto !important\",\n              padding: \"8px !important\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              className: \"qadpt-control-label\",\n              sx: {\n                padding: \"0 0 5px 0 !important\"\n              },\n              children: translate(\"Type\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: hotSpotProperties.Type,\n              onChange: e => onPropertyChange(\"Type\", e.target.value),\n              displayEmpty: true,\n              className: \"qadpt-control-input\",\n              sx: {\n                width: \"100% !important\",\n                borderRadius: \"12px\",\n                \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                  border: \"none\"\n                },\n                \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                  border: \"none\"\n                },\n                \"& fieldset\": {\n                  border: \"none\"\n                }\n              },\n              size: \"small\",\n              renderValue: selected => /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: \"flex\",\n                  justifyContent: \"space-between\",\n                  width: \"100%\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: translate(selected)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 12\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [selected === \"Info\" && /*#__PURE__*/_jsxDEV(\"span\", {\n                    dangerouslySetInnerHTML: {\n                      __html: InfoFilled\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 276,\n                    columnNumber: 37\n                  }, this), selected === \"Question\" && /*#__PURE__*/_jsxDEV(\"span\", {\n                    dangerouslySetInnerHTML: {\n                      __html: QuestionFill\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 277,\n                    columnNumber: 41\n                  }, this), selected === \"Solid\" && /*#__PURE__*/_jsxDEV(\"span\", {\n                    dangerouslySetInnerHTML: {\n                      __html: Solid\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 278,\n                    columnNumber: 38\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 12\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 11\n              }, this),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                id: \"qadpt-designpopup\",\n                value: \"Info\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: translate(\"Info\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 11\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    marginLeft: \"auto\",\n                    display: \"flex\",\n                    alignItems: \"center\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    dangerouslySetInnerHTML: {\n                      __html: InfoFilled\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 289,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 288,\n                  columnNumber: 11\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                id: \"qadpt-designpopup\",\n                value: \"Question\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: translate(\"Question\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 11\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    marginLeft: \"auto\",\n                    display: \"flex\",\n                    alignItems: \"center\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    dangerouslySetInnerHTML: {\n                      __html: QuestionFill\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 298,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 11\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                id: \"qadpt-designpopup\",\n                value: \"Solid\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: translate(\"Solid\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 11\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    marginLeft: \"auto\",\n                    display: \"flex\",\n                    alignItems: \"center\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    dangerouslySetInnerHTML: {\n                      __html: Solid\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 307,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 11\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            id: \"qadpt-designpopup\",\n            className: \"qadpt-control-box\",\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              id: \"qadpt-designpopup\",\n              className: \"qadpt-control-label\",\n              children: translate(\"Color\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"color\",\n              value: hotSpotProperties.Color,\n              onChange: e => onPropertyChange(\"Color\", e.target.value),\n              className: \"qadpt-color-input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            id: \"qadpt-designpopup\",\n            className: \"qadpt-control-box\",\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              id: \"qadpt-designpopup\",\n              className: \"qadpt-control-label\",\n              children: translate(\"Size\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n              id: \"qadpt-designpopup\",\n              variant: \"outlined\",\n              fullWidth: true,\n              className: \"qadpt-control-input\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                defaultValue: 1,\n                id: \"qadpt-designpopup\",\n                value: (hotSpotProperties.Size - 16) / 4 + 1,\n                onChange: e => handleSizeChange(Number(e.target.value)),\n                sx: {\n                  borderRadius: \"12px\",\n                  \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                    border: \"none\"\n                  },\n                  \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                    border: \"none\"\n                  },\n                  \"& fieldset\": {\n                    border: \"none\"\n                  }\n                },\n                children: [1, 2, 3, 4, 5, 6, 7, 8, 9].map(size => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  id: \"qadpt-designpopup\",\n                  value: size,\n                  children: size\n                }, size, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 12\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 10\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            id: \"qadpt-designpopup\",\n            className: \"qadpt-control-box\",\n            sx: {\n              flexDirection: \"column\",\n              height: \"auto !important\",\n              gap: \"8px\",\n              padding: \"8px !important\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              id: \"qadpt-designpopup\",\n              className: \"qadpt-control-item\",\n              sx: {\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"space-between\",\n                width: \"100%\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                className: \"qadpt-control-label\",\n                sx: {\n                  textAlign: \"left\",\n                  minWidth: \"130px\",\n                  padding: \"0 !important\"\n                },\n                children: translate(\"Pulse Animation\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"toggle-switch\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: hotSpotProperties.PulseAnimation,\n                  onChange: e => {\n                    onPropertyChange(\"PulseAnimation\", e.target.checked);\n                  },\n                  name: \"pulseAnimation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 5\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"slider\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 403,\n                  columnNumber: 5\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 9\n            }, this), hotSpotProperties.PulseAnimation && /*#__PURE__*/_jsxDEV(Box, {\n              id: \"qadpt-designpopup\",\n              className: \"qadpt-control-item\",\n              sx: {\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"space-between\",\n                width: \"100%\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                id: \"qadpt-designpopup\",\n                className: \"qadpt-control-label\",\n                sx: {\n                  textAlign: \"left\",\n                  minWidth: \"130px\",\n                  width: \"20px\",\n                  padding: \"0 !important\"\n                },\n                children: translate(\"Stop Animation Upon Interaction\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 11\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"toggle-switch\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: hotSpotProperties.stopAnimationUponInteraction,\n                  onChange: e => onPropertyChange(\"stopAnimationUponInteraction\", e.target.checked),\n                  name: \"stopAnimationUponInteraction\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 5\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"slider\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 431,\n                  columnNumber: 5\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 11\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 10\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 8\n          }, this), hotSpotProperties.ShowByDefault === false && /*#__PURE__*/_jsxDEV(Box, {\n            id: \"qadpt-designpopup\",\n            className: \"qadpt-control-box\",\n            sx: {\n              flexDirection: \"column\",\n              height: \"auto !important\",\n              padding: \"8px !important\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              className: \"qadpt-control-label\",\n              sx: {\n                padding: \"0 0 5px 0 !important\"\n              },\n              children: translate(\"Show Upon\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 10\n            }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n              variant: \"outlined\",\n              fullWidth: true,\n              className: \"qadpt-control-input\",\n              sx: {\n                borderRadius: \"12px\",\n                width: \"100% !important\",\n                \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                  border: \"none\"\n                },\n                \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                  border: \"none\"\n                },\n                \"& fieldset\": {\n                  border: \"none\"\n                },\n                \"&.MuiInputBase-root\": {\n                  border: \"1px solid #a8a8a8 !important\"\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                value: hotSpotProperties.ShowUpon // Ensure this value is correctly tied to the state\n                ,\n                onChange: e => onPropertyChange(\"ShowUpon\", e.target.value),\n                name: \"ShowUpon\",\n                sx: {\n                  width: \"100% !important\",\n                  borderRadius: \"12px\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"Clicking Hotspot\",\n                  children: translate(\"Clicking Hotspot\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 465,\n                  columnNumber: 12\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"Hovering Hotspot\",\n                  children: translate(\"Hovering Hotspot\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 466,\n                  columnNumber: 12\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 459,\n                columnNumber: 11\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 10\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 438,\n            columnNumber: 9\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              className: \"qadpt-control-label\",\n              children: translate(\"Show by Default\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"toggle-switch\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: hotSpotProperties.ShowByDefault,\n                onChange: e => onPropertyChange(\"ShowByDefault\", e.target.checked),\n                name: \"showByDefault\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 5\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"slider\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 483,\n                columnNumber: 5\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 471,\n            columnNumber: 8\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 7\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 6\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-drawerFooter\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: handleApplyChanges,\n          className: \"qadpt-btn\",\n          children: translate(\"Apply\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 490,\n          columnNumber: 7\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 489,\n        columnNumber: 6\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 5\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 152,\n    columnNumber: 4\n  }, this);\n};\n_s(HotspotSettings, \"aNPSSL3ZEAfbYB6fa+B7Pi0CufE=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c = HotspotSettings;\nexport default HotspotSettings;\nvar _c;\n$RefreshReg$(_c, \"HotspotSettings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "TextField", "IconButton", "<PERSON><PERSON>", "FormControl", "Select", "MenuItem", "CloseIcon", "useDrawerStore", "InfoFilled", "QuestionFill", "Reselect", "Solid", "ArrowBackIosNewOutlinedIcon", "useTranslation", "jsxDEV", "_jsxDEV", "HotspotSettings", "currentGuide", "onBack", "onClose", "_s", "t", "translate", "setHotspotPopup", "setElementSelected", "updatehotspots", "setDesignPopup", "toolTipGuideMetaData", "setOpenTooltip", "openTooltip", "currentStep", "setTooltipPositionByXpath", "updateCanvasInTooltip", "updateTooltipBtnContainer", "updateTooltipImageContainer", "pulseAnimationsH", "setPulseAnimationsH", "setIsUnSavedChanges", "state", "hotSpotProperties", "setHotSpotProperties", "_toolTipGuideMetaData", "currentStepIndex", "currentStepHotspots", "hotspots", "initialHotspotProperties", "XPosition", "YPosition", "Type", "Color", "Size", "PulseAnimation", "stopAnimationUponInteraction", "ShowUpon", "ShowByDefault", "_toolTipGuideMetaData2", "undefined", "handleClose", "handledesignclose", "handleSizeChange", "value", "sizeInPx", "onPropertyChange", "onReselectElement", "existingHotspot", "document", "getElementById", "existingTooltip", "allElementsWithOutline", "querySelectorAll", "for<PERSON>ach", "element", "style", "outline", "remove", "key", "prevState", "handleApplyChanges", "id", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "sx", "cursor", "dangerouslySetInnerHTML", "__html", "padding", "marginRight", "marginBottom", "fontSize", "paddingLeft", "margin", "variant", "onChange", "e", "target", "InputProps", "endAdornment", "border", "flexDirection", "height", "displayEmpty", "width", "borderRadius", "renderValue", "selected", "display", "justifyContent", "marginLeft", "alignItems", "type", "fullWidth", "defaultValue", "Number", "map", "gap", "textAlign", "min<PERSON><PERSON><PERSON>", "checked", "name", "_c", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/hotspot/HotspotSettings.tsx"], "sourcesContent": ["import React, { useReducer, useState,useEffect } from \"react\";\r\nimport { Box, Typography, TextField, Grid, IconButton, Button, InputAdornment, FormControl, InputLabel, Select, MenuItem, SelectChangeEvent, FormControlLabel, Switch } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport useDrawerStore, { BUTTON_CONT_DEF_VALUE_1, CANVAS_DEFAULT_VALUE, IMG_CONT_DEF_VALUE } from \"../../store/drawerStore\";\r\nimport { HOTSPOT_DEFAULT_VALUE } from \"../../store/drawerStore\";\r\nimport {\r\n  InfoFilled,\r\n  QuestionFill,\r\n  Reselect,\r\n  Solid,\r\n} from \"../../assets/icons/icons\";\r\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nconst HotspotSettings = ({ currentGuide,onBack,onClose }: any) => {\r\n\tconst { t: translate } = useTranslation();\r\n    const {\r\n\t\t\tsetHotspotPopup,\r\n\t\t\tsetElementSelected,\r\n\t\t\tupdatehotspots,\r\n\t\t\tsetDesignPopup,\r\n\t\t\ttoolTipGuideMetaData,\r\n\t\t\tsetOpenTooltip,\r\n\t\t\topenTooltip,\r\n\t\t\tcurrentStep,\r\n\t\t\tsetTooltipPositionByXpath,\r\n\t\t\tupdateCanvasInTooltip,\r\n\t\t\tupdateTooltipBtnContainer,\r\n\t\t\tupdateTooltipImageContainer,\r\n\t\t\tpulseAnimationsH,\r\n\t\tsetPulseAnimationsH,\r\n\t\t\tsetIsUnSavedChanges\r\n\t\t} = useDrawerStore((state: any) => state);\r\n\r\n\t\tconst [hotSpotProperties, setHotSpotProperties] = useState<any>(() => {\r\n\t\t\t// Get the current step's hotspot properties\r\n\t\t\tconst currentStepIndex = currentStep - 1;\r\n\t\t\tconst currentStepHotspots = toolTipGuideMetaData[currentStepIndex]?.hotspots;\r\n\r\n\t\t\t// Use the current step's hotspot properties if available, otherwise use default values\r\n\t\t\tconst initialHotspotProperties = currentStepHotspots || {\r\n\t\t\t\tXPosition: \"4\",\r\n\t\t\t\tYPosition: \"4\",\r\n\t\t\t\tType: \"Question\",\r\n\t\t\t\tColor: \"yellow\",\r\n\t\t\t\tSize: 16,\r\n\t\t\t\tPulseAnimation: pulseAnimationsH,\r\n\t\t\t\tstopAnimationUponInteraction: true,\r\n\t\t\t\tShowUpon: \"Hovering Hotspot\",\r\n\t\t\t\tShowByDefault: false,\r\n\t\t\t};\r\n\t\t\treturn initialHotspotProperties;\r\n\t\t});\r\n\r\n\t\t// Update hotspot properties when currentStep or toolTipGuideMetaData changes\r\n\t\tuseEffect(() => {\r\n\t\t\tconst currentStepIndex = currentStep - 1;\r\n\t\t\tconst currentStepHotspots = toolTipGuideMetaData[currentStepIndex]?.hotspots;\r\n\r\n\t\t\tif (currentStepHotspots) {\r\n\t\t\t\t// Update local state with the current step's hotspot properties\r\n\t\t\t\tsetHotSpotProperties({\r\n\t\t\t\t\tXPosition: currentStepHotspots.XPosition || \"4\",\r\n\t\t\t\t\tYPosition: currentStepHotspots.YPosition || \"4\",\r\n\t\t\t\t\tType: currentStepHotspots.Type || \"Question\",\r\n\t\t\t\t\tColor: currentStepHotspots.Color || \"yellow\",\r\n\t\t\t\t\tSize: currentStepHotspots.Size || 16,\r\n\t\t\t\t\tPulseAnimation: currentStepHotspots.PulseAnimation !== undefined ? currentStepHotspots.PulseAnimation : pulseAnimationsH,\r\n\t\t\t\t\tstopAnimationUponInteraction: currentStepHotspots.stopAnimationUponInteraction !== undefined ? currentStepHotspots.stopAnimationUponInteraction : true,\r\n\t\t\t\t\tShowUpon: currentStepHotspots.ShowUpon || \"Hovering Hotspot\",\r\n\t\t\t\t\tShowByDefault: currentStepHotspots.ShowByDefault || false,\r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\t// If no hotspot data exists for this step, use default values\r\n\t\t\t\tsetHotSpotProperties({\r\n\t\t\t\t\tXPosition: \"4\",\r\n\t\t\t\t\tYPosition: \"4\",\r\n\t\t\t\t\tType: \"Question\",\r\n\t\t\t\t\tColor: \"yellow\",\r\n\t\t\t\t\tSize: 16,\r\n\t\t\t\t\tPulseAnimation: pulseAnimationsH,\r\n\t\t\t\t\tstopAnimationUponInteraction: true,\r\n\t\t\t\t\tShowUpon: \"Hovering Hotspot\",\r\n\t\t\t\t\tShowByDefault: false,\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}, [currentStep, toolTipGuideMetaData, pulseAnimationsH]);\r\n\r\n\t\tconst handleClose = () => {\r\n\t\t\tsetHotspotPopup(false);\r\n\t\t};\r\n\t\tconst handledesignclose = () => {\r\n\t\t\tsetDesignPopup(false);\r\n\t\t};\r\n\t\tconst handleSizeChange = (value: number) => {\r\n\t\t\tconst sizeInPx = 16 + (value - 1) * 4;\r\n\t\t\tonPropertyChange(\"Size\", sizeInPx);\r\n\t\t};\r\n\r\n\t\tconst onReselectElement = () => {\r\n\t\t\t// setHotSpotProperties({\r\n\t\t\t// \tXPosition: \"4\",\r\n\t\t\t// \tYPosition: \"4\",\r\n\t\t\t// \tType: \"Question\",\r\n\t\t\t// \tColor: \"y4ellow\",\r\n\t\t\t// \tSize: 1,\r\n\t\t\t// \tPulseAnimation: pulseAnimationsH,\r\n\t\t\t// \tstopAnimationUponInteraction: true,\r\n\t\t\t// \tShowUpon: \"Hovering Hotspot\",\r\n\t\t\t// \tShowByDefault: false,\r\n\t\t\t// });\r\n\t\t\tsetElementSelected(false);\r\n\t\t\thandledesignclose();\r\n\t\t\t//updatehotspots(HOTSPOT_DEFAULT_VALUE);\r\n\t\t\t// updateCanvasInTooltip(CANVAS_DEFAULT_VALUE);\r\n\t\t\t//  updateTooltipBtnContainer(BUTTON_CONT_DEF_VALUE_1);\r\n\t\t\t//  updateTooltipImageContainer(IMG_CONT_DEF_VALUE);\r\n\t\t\tconst existingHotspot = document.getElementById(\"hotspotBlinkCreation\");\r\n\t\t\tconst existingTooltip = document.getElementById(\"Tooltip-unique\");\r\n\r\n\t\t\tconst allElementsWithOutline = document.querySelectorAll<HTMLElement>(\"[style*='outline']\");\r\n\t\t\tallElementsWithOutline.forEach((element) => {\r\n\t\t\t\telement.style.outline = \"\"; // Reset the outline (border) color\r\n\t\t\t});\r\n\r\n\t\t\tif (existingHotspot) {\r\n\t\t\t\texistingHotspot.remove();\r\n\t\t\t}\r\n\t\t\t// if (existingTooltip)\r\n\t\t\t// {\r\n\t\t\t//   existingTooltip.remove();\r\n\t\t\t// }\r\n\t\t\tsetHotspotPopup(false);\r\n\t\t\tsetIsUnSavedChanges(true);\r\n\t\t};\r\n\r\n\t\tconst onPropertyChange = (key: any, value: any) => {\r\n\t\t\tsetHotSpotProperties((prevState: any) => ({\r\n\t\t\t\t...prevState,\r\n\t\t\t\t[key]: value,\r\n\t\t\t}));\r\n\t\t};\r\n\r\n\t\tconst handleApplyChanges = () => {\r\n\t\t\tupdatehotspots(hotSpotProperties);\r\n\t\t\thandleClose();\r\n\t\t\tsetHotspotPopup(false);\r\n\t\t\tsetIsUnSavedChanges(true);\r\n\t\t};\r\n\r\n\t\treturn (\r\n\t\t\t<div\r\n\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\tclassName=\"qadpt-designpopup\"\r\n\t\t\t>\r\n\t\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\taria-label={translate(\"back\")}\r\n\t\t\t\t\t\t\tonClick={onBack}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<ArrowBackIosNewOutlinedIcon />\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t<div className=\"qadpt-title\">{translate(\"Hotspot\")}</div>\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\taria-label={translate(\"Close\")}\r\n\t\t\t\t\t\t\tonClick={onClose}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div className=\"qadpt-canblock\">\r\n\t\t\t\t\t\t<div className=\"qadpt-controls\">\r\n\t\t\t\t\t\t\t<Box className=\"qadpt-control-box\" sx={{ cursor: \"pointer\" }}>\r\n\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Reselect Element\")}</Typography>\r\n\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\tonClick={onReselectElement}\r\n\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: Reselect }}\r\n\t\t\t\t\t\t\t\t\tstyle={{  padding: \"5px\", marginRight: \"10px\" }}\r\n\t\t\t\t\t\t\t\t/>{\" \"}\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t<Box className=\"qadpt-position-grid\">\r\n\t\t\t\t\t\t\t\t<Typography className=\"qadpt-ctrl-title\">{translate(\"Position within Element\")}</Typography>\r\n\r\n\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-controls\"\r\n\t\t\t\t\t\t\t\t\tstyle={{ padding: \"0px\" }}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{ padding: \"0 !important\",marginBottom:\"0 !important\" }}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{ fontSize: \"12px !important\", paddingLeft: \"0 !important\", margin: \"3px\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\tX {translate(\"Axis Offset\")}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\t\tvalue={hotSpotProperties.XPosition}\r\n\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"XPosition\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tendAdornment: \"%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" }, \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{ padding: \"0 !important\",marginBottom:\"0 !important\" }}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{ fontSize: \"12px !important\", paddingLeft: \"0 !important\", margin: \"3px\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\tY {translate(\"Axis Offset\")}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\t\tvalue={hotSpotProperties.YPosition}\r\n\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"YPosition\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tendAdornment: \"%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" }, \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\tsx={{ flexDirection: \"column\", height: \"auto !important\",padding:\"8px !important\" }}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\" sx={{ padding: \"0 0 5px 0 !important\" }}>{translate(\"Type\")}</Typography>\r\n\t\t\t\t\t\t\t\t<Select\r\n\t\t\t\t\t\t\t\t\tvalue={hotSpotProperties.Type}\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"Type\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\tdisplayEmpty\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\twidth :\"100% !important\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"& fieldset\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t  }}\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\trenderValue={(selected) => (\r\n\t\t\t\t\t\t\t\t\t\t<Box sx={{ display: \"flex\", justifyContent: \"space-between\", width: \"100%\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t<span>{translate(selected)}</span>\r\n\t\t\t\t\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{selected === \"Info\" && <span dangerouslySetInnerHTML={{ __html: InfoFilled }} />}\r\n\t\t\t\t\t\t\t\t\t\t\t\t{selected === \"Question\" && <span dangerouslySetInnerHTML={{ __html: QuestionFill }} />}\r\n\t\t\t\t\t\t\t\t\t\t\t\t{selected === \"Solid\" && <span dangerouslySetInnerHTML={{ __html: Solid }} />}\r\n\t\t\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<MenuItem\r\n\t\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\t\tvalue=\"Info\"\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<span>{translate(\"Info\")}</span>\r\n\t\t\t\t\t\t\t\t\t\t<span style={{ marginLeft: \"auto\", display: \"flex\", alignItems: \"center\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: InfoFilled }} />\r\n\t\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t</MenuItem>\r\n\t\t\t\t\t\t\t\t\t<MenuItem\r\n\t\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\t\tvalue=\"Question\"\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<span>{translate(\"Question\")}</span>\r\n\t\t\t\t\t\t\t\t\t\t<span style={{ marginLeft: \"auto\", display: \"flex\", alignItems: \"center\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: QuestionFill }} />\r\n\t\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t</MenuItem>\r\n\t\t\t\t\t\t\t\t\t<MenuItem\r\n\t\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\t\tvalue=\"Solid\"\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<span>{translate(\"Solid\")}</span>\r\n\t\t\t\t\t\t\t\t\t\t<span style={{ marginLeft: \"auto\", display: \"flex\", alignItems: \"center\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: Solid }} />\r\n\t\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t</MenuItem>\r\n\t\t\t\t\t\t\t\t</Select>\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{translate(\"Color\")}\r\n\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\t\tvalue={hotSpotProperties.Color}\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"Color\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{translate(\"Size\")}\r\n\t\t\t\t\t\t\t\t</Typography>\r\n\r\n\t\t\t\t\t\t\t\t<FormControl\r\n\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<Select\r\n\t\t\t\t\t\t\t\t\t\tdefaultValue={1}\r\n\t\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\t\tvalue={(hotSpotProperties.Size - 16) / 4 + 1}\r\n\t\t\t\t\t\t\t\t\t\tonChange={(e) => handleSizeChange(Number(e.target.value))}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t  }}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{[1, 2, 3, 4, 5, 6, 7, 8, 9].map((size) => (\r\n\t\t\t\t\t\t\t\t\t\t\t<MenuItem\r\n\t\t\t\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tkey={size}\r\n\t\t\t\t\t\t\t\t\t\t\t\tvalue={size}\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{size}\r\n\t\t\t\t\t\t\t\t\t\t\t</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t\t</Select>\r\n\t\t\t\t\t\t\t\t</FormControl>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\tsx={{ flexDirection: \"column\", height: \"auto !important\", gap: \"8px\",padding:\"8px !important\" }}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{/* Pulse Animation Toggle */}\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-item\"\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent: \"space-between\",\r\n\t\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{ textAlign: \"left\", minWidth: \"130px\" ,padding:\"0 !important\"}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{translate(\"Pulse Animation\")}\r\n\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t<label className=\"toggle-switch\">\r\n    <input\r\n        type=\"checkbox\"\r\n        checked={hotSpotProperties.PulseAnimation}\r\n        onChange={(e) => {\r\n            onPropertyChange(\"PulseAnimation\", e.target.checked);\r\n        }}\r\n        name=\"pulseAnimation\"\r\n    />\r\n    <span className=\"slider\"></span>\r\n</label>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t{hotSpotProperties.PulseAnimation && (\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-item\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\tjustifyContent: \"space-between\",\r\n\t\t\t\t\t\t\t\t\t\t\twidth:\"100%\"\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{ textAlign: \"left\", minWidth: \"130px\",width:\"20px\",padding:\"0 !important\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{translate(\"Stop Animation Upon Interaction\")}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t<label className=\"toggle-switch\">\r\n    <input\r\n        type=\"checkbox\"\r\n        checked={hotSpotProperties.stopAnimationUponInteraction}\r\n        onChange={(e) => onPropertyChange(\"stopAnimationUponInteraction\", e.target.checked)}\r\n        name=\"stopAnimationUponInteraction\"\r\n    />\r\n    <span className=\"slider\"></span>\r\n</label>\r\n\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t{hotSpotProperties.ShowByDefault === false && (\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\tsx={{ flexDirection: \"column\", height: \"auto !important\" ,padding:\"8px !important\"}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\" sx={{ padding: \"0 0 5px 0 !important\" }}>{translate(\"Show Upon\")}</Typography>\r\n\r\n\t\t\t\t\t\t\t\t\t{/* Show Upon Dropdown */}\r\n\t\t\t\t\t\t\t\t\t<FormControl\r\n\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\t\twidth: \"100% !important\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"&.MuiInputBase-root\":{border : \"1px solid #a8a8a8 !important\"}\r\n\t\t\t\t\t\t\t\t\t\t  }}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<Select\r\n\t\t\t\t\t\t\t\t\t\t\tvalue={hotSpotProperties.ShowUpon} // Ensure this value is correctly tied to the state\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={(e: any) => onPropertyChange(\"ShowUpon\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\t\tname=\"ShowUpon\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{ width: \"100% !important\", borderRadius: \"12px\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"Clicking Hotspot\">{translate(\"Clicking Hotspot\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"Hovering Hotspot\">{translate(\"Hovering Hotspot\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t</Select>\r\n\t\t\t\t\t\t\t\t\t</FormControl>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Show by Default\")}</Typography>\r\n\r\n\t\t\t\t\t\t\t\t{/* Show by Default Toggle */}\r\n\r\n\t\t\t\t\t\t\t\t<label className=\"toggle-switch\">\r\n    <input\r\n        type=\"checkbox\"\r\n        checked={hotSpotProperties.ShowByDefault}\r\n        onChange={(e) => onPropertyChange(\"ShowByDefault\", e.target.checked)}\r\n        name=\"showByDefault\"\r\n    />\r\n    <span className=\"slider\"></span>\r\n</label>\r\n\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div className=\"qadpt-drawerFooter\">\r\n\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\tonClick={handleApplyChanges}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-btn\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{translate(\"Apply\")}\r\n\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t);\r\n};\r\n\r\nexport default HotspotSettings;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAgBC,QAAQ,EAACC,SAAS,QAAQ,OAAO;AAC7D,SAASC,GAAG,EAAEC,UAAU,EAAEC,SAAS,EAAQC,UAAU,EAAEC,MAAM,EAAkBC,WAAW,EAAcC,MAAM,EAAEC,QAAQ,QAAqD,eAAe;AAC5L,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,cAAc,MAA6E,yBAAyB;AAE3H,SACEC,UAAU,EACVC,YAAY,EACZC,QAAQ,EACRC,KAAK,QACA,0BAA0B;AACjC,OAAOC,2BAA2B,MAAM,6CAA6C;AACrF,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,eAAe,GAAGA,CAAC;EAAEC,YAAY;EAACC,MAAM;EAACC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACjE,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGT,cAAc,CAAC,CAAC;EACtC,MAAM;IACPU,eAAe;IACfC,kBAAkB;IAClBC,cAAc;IACdC,cAAc;IACdC,oBAAoB;IACpBC,cAAc;IACdC,WAAW;IACXC,WAAW;IACXC,yBAAyB;IACzBC,qBAAqB;IACrBC,yBAAyB;IACzBC,2BAA2B;IAC3BC,gBAAgB;IACjBC,mBAAmB;IAClBC;EACD,CAAC,GAAG9B,cAAc,CAAE+B,KAAU,IAAKA,KAAK,CAAC;EAEzC,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5C,QAAQ,CAAM,MAAM;IAAA,IAAA6C,qBAAA;IACrE;IACA,MAAMC,gBAAgB,GAAGZ,WAAW,GAAG,CAAC;IACxC,MAAMa,mBAAmB,IAAAF,qBAAA,GAAGd,oBAAoB,CAACe,gBAAgB,CAAC,cAAAD,qBAAA,uBAAtCA,qBAAA,CAAwCG,QAAQ;;IAE5E;IACA,MAAMC,wBAAwB,GAAGF,mBAAmB,IAAI;MACvDG,SAAS,EAAE,GAAG;MACdC,SAAS,EAAE,GAAG;MACdC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,QAAQ;MACfC,IAAI,EAAE,EAAE;MACRC,cAAc,EAAEhB,gBAAgB;MAChCiB,4BAA4B,EAAE,IAAI;MAClCC,QAAQ,EAAE,kBAAkB;MAC5BC,aAAa,EAAE;IAChB,CAAC;IACD,OAAOT,wBAAwB;EAChC,CAAC,CAAC;;EAEF;EACAhD,SAAS,CAAC,MAAM;IAAA,IAAA0D,sBAAA;IACf,MAAMb,gBAAgB,GAAGZ,WAAW,GAAG,CAAC;IACxC,MAAMa,mBAAmB,IAAAY,sBAAA,GAAG5B,oBAAoB,CAACe,gBAAgB,CAAC,cAAAa,sBAAA,uBAAtCA,sBAAA,CAAwCX,QAAQ;IAE5E,IAAID,mBAAmB,EAAE;MACxB;MACAH,oBAAoB,CAAC;QACpBM,SAAS,EAAEH,mBAAmB,CAACG,SAAS,IAAI,GAAG;QAC/CC,SAAS,EAAEJ,mBAAmB,CAACI,SAAS,IAAI,GAAG;QAC/CC,IAAI,EAAEL,mBAAmB,CAACK,IAAI,IAAI,UAAU;QAC5CC,KAAK,EAAEN,mBAAmB,CAACM,KAAK,IAAI,QAAQ;QAC5CC,IAAI,EAAEP,mBAAmB,CAACO,IAAI,IAAI,EAAE;QACpCC,cAAc,EAAER,mBAAmB,CAACQ,cAAc,KAAKK,SAAS,GAAGb,mBAAmB,CAACQ,cAAc,GAAGhB,gBAAgB;QACxHiB,4BAA4B,EAAET,mBAAmB,CAACS,4BAA4B,KAAKI,SAAS,GAAGb,mBAAmB,CAACS,4BAA4B,GAAG,IAAI;QACtJC,QAAQ,EAAEV,mBAAmB,CAACU,QAAQ,IAAI,kBAAkB;QAC5DC,aAAa,EAAEX,mBAAmB,CAACW,aAAa,IAAI;MACrD,CAAC,CAAC;IACH,CAAC,MAAM;MACN;MACAd,oBAAoB,CAAC;QACpBM,SAAS,EAAE,GAAG;QACdC,SAAS,EAAE,GAAG;QACdC,IAAI,EAAE,UAAU;QAChBC,KAAK,EAAE,QAAQ;QACfC,IAAI,EAAE,EAAE;QACRC,cAAc,EAAEhB,gBAAgB;QAChCiB,4BAA4B,EAAE,IAAI;QAClCC,QAAQ,EAAE,kBAAkB;QAC5BC,aAAa,EAAE;MAChB,CAAC,CAAC;IACH;EACD,CAAC,EAAE,CAACxB,WAAW,EAAEH,oBAAoB,EAAEQ,gBAAgB,CAAC,CAAC;EAEzD,MAAMsB,WAAW,GAAGA,CAAA,KAAM;IACzBlC,eAAe,CAAC,KAAK,CAAC;EACvB,CAAC;EACD,MAAMmC,iBAAiB,GAAGA,CAAA,KAAM;IAC/BhC,cAAc,CAAC,KAAK,CAAC;EACtB,CAAC;EACD,MAAMiC,gBAAgB,GAAIC,KAAa,IAAK;IAC3C,MAAMC,QAAQ,GAAG,EAAE,GAAG,CAACD,KAAK,GAAG,CAAC,IAAI,CAAC;IACrCE,gBAAgB,CAAC,MAAM,EAAED,QAAQ,CAAC;EACnC,CAAC;EAED,MAAME,iBAAiB,GAAGA,CAAA,KAAM;IAC/B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAvC,kBAAkB,CAAC,KAAK,CAAC;IACzBkC,iBAAiB,CAAC,CAAC;IACnB;IACA;IACA;IACA;IACA,MAAMM,eAAe,GAAGC,QAAQ,CAACC,cAAc,CAAC,sBAAsB,CAAC;IACvE,MAAMC,eAAe,GAAGF,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC;IAEjE,MAAME,sBAAsB,GAAGH,QAAQ,CAACI,gBAAgB,CAAc,oBAAoB,CAAC;IAC3FD,sBAAsB,CAACE,OAAO,CAAEC,OAAO,IAAK;MAC3CA,OAAO,CAACC,KAAK,CAACC,OAAO,GAAG,EAAE,CAAC,CAAC;IAC7B,CAAC,CAAC;IAEF,IAAIT,eAAe,EAAE;MACpBA,eAAe,CAACU,MAAM,CAAC,CAAC;IACzB;IACA;IACA;IACA;IACA;IACAnD,eAAe,CAAC,KAAK,CAAC;IACtBc,mBAAmB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMyB,gBAAgB,GAAGA,CAACa,GAAQ,EAAEf,KAAU,KAAK;IAClDpB,oBAAoB,CAAEoC,SAAc,KAAM;MACzC,GAAGA,SAAS;MACZ,CAACD,GAAG,GAAGf;IACR,CAAC,CAAC,CAAC;EACJ,CAAC;EAED,MAAMiB,kBAAkB,GAAGA,CAAA,KAAM;IAChCpD,cAAc,CAACc,iBAAiB,CAAC;IACjCkB,WAAW,CAAC,CAAC;IACblC,eAAe,CAAC,KAAK,CAAC;IACtBc,mBAAmB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,oBACCtB,OAAA;IACC+D,EAAE,EAAC,mBAAmB;IACtBC,SAAS,EAAC,mBAAmB;IAAAC,QAAA,eAE7BjE,OAAA;MAAKgE,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC7BjE,OAAA;QAAKgE,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBACnCjE,OAAA,CAACd,UAAU;UACV,cAAYqB,SAAS,CAAC,MAAM,CAAE;UAC9B2D,OAAO,EAAE/D,MAAO;UAAA8D,QAAA,eAEhBjE,OAAA,CAACH,2BAA2B;YAAAsE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACbtE,OAAA;UAAKgE,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAE1D,SAAS,CAAC,SAAS;QAAC;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACzDtE,OAAA,CAACd,UAAU;UACVqF,IAAI,EAAC,OAAO;UACZ,cAAYhE,SAAS,CAAC,OAAO,CAAE;UAC/B2D,OAAO,EAAE9D,OAAQ;UAAA6D,QAAA,eAEjBjE,OAAA,CAACT,SAAS;YAAA4E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACNtE,OAAA;QAAKgE,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC9BjE,OAAA;UAAKgE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC9BjE,OAAA,CAACjB,GAAG;YAACiF,SAAS,EAAC,mBAAmB;YAACQ,EAAE,EAAE;cAAEC,MAAM,EAAE;YAAU,CAAE;YAAAR,QAAA,gBAC5DjE,OAAA,CAAChB,UAAU;cAACgF,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAE1D,SAAS,CAAC,kBAAkB;YAAC;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACxFtE,OAAA;cACCkE,OAAO,EAAElB,iBAAkB;cAC3B0B,uBAAuB,EAAE;gBAAEC,MAAM,EAAEhF;cAAS,CAAE;cAC9C8D,KAAK,EAAE;gBAAGmB,OAAO,EAAE,KAAK;gBAAEC,WAAW,EAAE;cAAO;YAAE;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,EAAC,GAAG;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACNtE,OAAA,CAACjB,GAAG;YAACiF,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBACnCjE,OAAA,CAAChB,UAAU;cAACgF,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAE1D,SAAS,CAAC,yBAAyB;YAAC;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAE5FtE,OAAA;cACCgE,SAAS,EAAC,gBAAgB;cAC1BP,KAAK,EAAE;gBAAEmB,OAAO,EAAE;cAAM,CAAE;cAAAX,QAAA,gBAE1BjE,OAAA,CAACjB,GAAG;gBACHiF,SAAS,EAAC,mBAAmB;gBAC7BQ,EAAE,EAAE;kBAAEI,OAAO,EAAE,cAAc;kBAACE,YAAY,EAAC;gBAAe,CAAE;gBAAAb,QAAA,gBAE5DjE,OAAA,CAAChB,UAAU;kBACVgF,SAAS,EAAC,qBAAqB;kBAC/BQ,EAAE,EAAE;oBAAEO,QAAQ,EAAE,iBAAiB;oBAAEC,WAAW,EAAE,cAAc;oBAAEC,MAAM,EAAE;kBAAM,CAAE;kBAAAhB,QAAA,GAChF,IACE,EAAC1D,SAAS,CAAC,aAAa,CAAC;gBAAA;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,eACbtE,OAAA,CAACf,SAAS;kBACTiG,OAAO,EAAC,UAAU;kBAClBrC,KAAK,EAAErB,iBAAiB,CAACO,SAAU;kBAEnCwC,IAAI,EAAC,OAAO;kBACZP,SAAS,EAAC,qBAAqB;kBAC/BmB,QAAQ,EAAGC,CAAC,IAAKrC,gBAAgB,CAAC,WAAW,EAAEqC,CAAC,CAACC,MAAM,CAACxC,KAAK,CAAE;kBAC/DyC,UAAU,EAAE;oBACXC,YAAY,EAAE,GAAG;oBACjBf,EAAE,EAAE;sBAEH,0CAA0C,EAAE;wBAAEgB,MAAM,EAAE;sBAAO,CAAC;sBAC9D,gDAAgD,EAAE;wBAAEA,MAAM,EAAE;sBAAO,CAAC;sBACpE,YAAY,EAAC;wBAACA,MAAM,EAAC;sBAAM;oBAE5B;kBACD;gBAAE;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNtE,OAAA,CAACjB,GAAG;gBACHiF,SAAS,EAAC,mBAAmB;gBAC7BQ,EAAE,EAAE;kBAAEI,OAAO,EAAE,cAAc;kBAACE,YAAY,EAAC;gBAAe,CAAE;gBAAAb,QAAA,gBAE5DjE,OAAA,CAAChB,UAAU;kBACVgF,SAAS,EAAC,qBAAqB;kBAC/BQ,EAAE,EAAE;oBAAEO,QAAQ,EAAE,iBAAiB;oBAAEC,WAAW,EAAE,cAAc;oBAAEC,MAAM,EAAE;kBAAM,CAAE;kBAAAhB,QAAA,GAChF,IACE,EAAC1D,SAAS,CAAC,aAAa,CAAC;gBAAA;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,eACbtE,OAAA,CAACf,SAAS;kBACTiG,OAAO,EAAC,UAAU;kBAClBrC,KAAK,EAAErB,iBAAiB,CAACQ,SAAU;kBAEnCuC,IAAI,EAAC,OAAO;kBACZP,SAAS,EAAC,qBAAqB;kBAC/BmB,QAAQ,EAAGC,CAAC,IAAKrC,gBAAgB,CAAC,WAAW,EAAEqC,CAAC,CAACC,MAAM,CAACxC,KAAK,CAAE;kBAC/DyC,UAAU,EAAE;oBACXC,YAAY,EAAE,GAAG;oBACjBf,EAAE,EAAE;sBAEH,0CAA0C,EAAE;wBAAEgB,MAAM,EAAE;sBAAO,CAAC;sBAC9D,gDAAgD,EAAE;wBAAEA,MAAM,EAAE;sBAAO,CAAC;sBACpE,YAAY,EAAC;wBAACA,MAAM,EAAC;sBAAM;oBAE5B;kBACD;gBAAE;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAENtE,OAAA,CAACjB,GAAG;YACHgF,EAAE,EAAC,mBAAmB;YACtBC,SAAS,EAAC,mBAAmB;YAC7BQ,EAAE,EAAE;cAAEiB,aAAa,EAAE,QAAQ;cAAEC,MAAM,EAAE,iBAAiB;cAACd,OAAO,EAAC;YAAiB,CAAE;YAAAX,QAAA,gBAEpFjE,OAAA,CAAChB,UAAU;cAACgF,SAAS,EAAC,qBAAqB;cAACQ,EAAE,EAAE;gBAAEI,OAAO,EAAE;cAAuB,CAAE;cAAAX,QAAA,EAAE1D,SAAS,CAAC,MAAM;YAAC;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACrHtE,OAAA,CAACX,MAAM;cACNwD,KAAK,EAAErB,iBAAiB,CAACS,IAAK;cAC9BkD,QAAQ,EAAGC,CAAC,IAAKrC,gBAAgB,CAAC,MAAM,EAAEqC,CAAC,CAACC,MAAM,CAACxC,KAAK,CAAE;cAC1D8C,YAAY;cACZ3B,SAAS,EAAC,qBAAqB;cAE/BQ,EAAE,EAAE;gBACHoB,KAAK,EAAE,iBAAiB;gBACxBC,YAAY,EAAE,MAAM;gBACpB,0CAA0C,EAAE;kBAAEL,MAAM,EAAE;gBAAO,CAAC;gBAC9D,gDAAgD,EAAE;kBAAEA,MAAM,EAAE;gBAAO,CAAC;gBACpE,YAAY,EAAE;kBAAEA,MAAM,EAAE;gBAAO;cAE9B,CAAE;cACJjB,IAAI,EAAC,OAAO;cACZuB,WAAW,EAAGC,QAAQ,iBACrB/F,OAAA,CAACjB,GAAG;gBAACyF,EAAE,EAAE;kBAAEwB,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEL,KAAK,EAAE;gBAAO,CAAE;gBAAA3B,QAAA,gBAC5EjE,OAAA;kBAAAiE,QAAA,EAAO1D,SAAS,CAACwF,QAAQ;gBAAC;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClCtE,OAAA;kBAAAiE,QAAA,GACE8B,QAAQ,KAAK,MAAM,iBAAI/F,OAAA;oBAAM0E,uBAAuB,EAAE;sBAAEC,MAAM,EAAElF;oBAAW;kBAAE;oBAAA0E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EAChFyB,QAAQ,KAAK,UAAU,iBAAI/F,OAAA;oBAAM0E,uBAAuB,EAAE;sBAAEC,MAAM,EAAEjF;oBAAa;kBAAE;oBAAAyE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACtFyB,QAAQ,KAAK,OAAO,iBAAI/F,OAAA;oBAAM0E,uBAAuB,EAAE;sBAAEC,MAAM,EAAE/E;oBAAM;kBAAE;oBAAAuE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACJ;cAAAL,QAAA,gBAEFjE,OAAA,CAACV,QAAQ;gBACRyE,EAAE,EAAC,mBAAmB;gBACtBlB,KAAK,EAAC,MAAM;gBAAAoB,QAAA,gBAEZjE,OAAA;kBAAAiE,QAAA,EAAO1D,SAAS,CAAC,MAAM;gBAAC;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAChCtE,OAAA;kBAAMyD,KAAK,EAAE;oBAAEyC,UAAU,EAAE,MAAM;oBAAEF,OAAO,EAAE,MAAM;oBAAEG,UAAU,EAAE;kBAAS,CAAE;kBAAAlC,QAAA,eAC1EjE,OAAA;oBAAM0E,uBAAuB,EAAE;sBAAEC,MAAM,EAAElF;oBAAW;kBAAE;oBAAA0E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACXtE,OAAA,CAACV,QAAQ;gBACRyE,EAAE,EAAC,mBAAmB;gBACtBlB,KAAK,EAAC,UAAU;gBAAAoB,QAAA,gBAEhBjE,OAAA;kBAAAiE,QAAA,EAAO1D,SAAS,CAAC,UAAU;gBAAC;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACpCtE,OAAA;kBAAMyD,KAAK,EAAE;oBAAEyC,UAAU,EAAE,MAAM;oBAAEF,OAAO,EAAE,MAAM;oBAAEG,UAAU,EAAE;kBAAS,CAAE;kBAAAlC,QAAA,eAC1EjE,OAAA;oBAAM0E,uBAAuB,EAAE;sBAAEC,MAAM,EAAEjF;oBAAa;kBAAE;oBAAAyE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACXtE,OAAA,CAACV,QAAQ;gBACRyE,EAAE,EAAC,mBAAmB;gBACtBlB,KAAK,EAAC,OAAO;gBAAAoB,QAAA,gBAEbjE,OAAA;kBAAAiE,QAAA,EAAO1D,SAAS,CAAC,OAAO;gBAAC;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjCtE,OAAA;kBAAMyD,KAAK,EAAE;oBAAEyC,UAAU,EAAE,MAAM;oBAAEF,OAAO,EAAE,MAAM;oBAAEG,UAAU,EAAE;kBAAS,CAAE;kBAAAlC,QAAA,eAC1EjE,OAAA;oBAAM0E,uBAAuB,EAAE;sBAAEC,MAAM,EAAE/E;oBAAM;kBAAE;oBAAAuE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENtE,OAAA,CAACjB,GAAG;YACHgF,EAAE,EAAC,mBAAmB;YACtBC,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAE7BjE,OAAA,CAAChB,UAAU;cACV+E,EAAE,EAAC,mBAAmB;cACtBC,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAE9B1D,SAAS,CAAC,OAAO;YAAC;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eACbtE,OAAA;cACCoG,IAAI,EAAC,OAAO;cACZvD,KAAK,EAAErB,iBAAiB,CAACU,KAAM;cAC/BiD,QAAQ,EAAGC,CAAC,IAAKrC,gBAAgB,CAAC,OAAO,EAAEqC,CAAC,CAACC,MAAM,CAACxC,KAAK,CAAE;cAC3DmB,SAAS,EAAC;YAAmB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENtE,OAAA,CAACjB,GAAG;YACHgF,EAAE,EAAC,mBAAmB;YACtBC,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAE7BjE,OAAA,CAAChB,UAAU;cACV+E,EAAE,EAAC,mBAAmB;cACtBC,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAE9B1D,SAAS,CAAC,MAAM;YAAC;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eAEbtE,OAAA,CAACZ,WAAW;cACX2E,EAAE,EAAC,mBAAmB;cACtBmB,OAAO,EAAC,UAAU;cAClBmB,SAAS;cACTrC,SAAS,EAAC,qBAAqB;cAAAC,QAAA,eAE/BjE,OAAA,CAACX,MAAM;gBACNiH,YAAY,EAAE,CAAE;gBAChBvC,EAAE,EAAC,mBAAmB;gBACtBlB,KAAK,EAAE,CAACrB,iBAAiB,CAACW,IAAI,GAAG,EAAE,IAAI,CAAC,GAAG,CAAE;gBAC7CgD,QAAQ,EAAGC,CAAC,IAAKxC,gBAAgB,CAAC2D,MAAM,CAACnB,CAAC,CAACC,MAAM,CAACxC,KAAK,CAAC,CAAE;gBAC1D2B,EAAE,EAAE;kBACHqB,YAAY,EAAE,MAAM;kBACpB,0CAA0C,EAAE;oBAAEL,MAAM,EAAE;kBAAO,CAAC;kBAC9D,gDAAgD,EAAE;oBAAEA,MAAM,EAAE;kBAAO,CAAC;kBACpE,YAAY,EAAE;oBAAEA,MAAM,EAAE;kBAAO;gBAC9B,CAAE;gBAAAvB,QAAA,EAEH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACuC,GAAG,CAAEjC,IAAI,iBACrCvE,OAAA,CAACV,QAAQ;kBACRyE,EAAE,EAAC,mBAAmB;kBAEtBlB,KAAK,EAAE0B,IAAK;kBAAAN,QAAA,EAEXM;gBAAI,GAHAA,IAAI;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAIA,CACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNtE,OAAA,CAACjB,GAAG;YACHgF,EAAE,EAAC,mBAAmB;YACtBC,SAAS,EAAC,mBAAmB;YAC7BQ,EAAE,EAAE;cAAEiB,aAAa,EAAE,QAAQ;cAAEC,MAAM,EAAE,iBAAiB;cAAEe,GAAG,EAAE,KAAK;cAAC7B,OAAO,EAAC;YAAiB,CAAE;YAAAX,QAAA,gBAGhGjE,OAAA,CAACjB,GAAG;cACHgF,EAAE,EAAC,mBAAmB;cACtBC,SAAS,EAAC,oBAAoB;cAC9BQ,EAAE,EAAE;gBACHwB,OAAO,EAAE,MAAM;gBACfG,UAAU,EAAE,QAAQ;gBACpBF,cAAc,EAAE,eAAe;gBAC/BL,KAAK,EAAE;cACR,CAAE;cAAA3B,QAAA,gBAEFjE,OAAA,CAAChB,UAAU;gBACVgF,SAAS,EAAC,qBAAqB;gBAC/BQ,EAAE,EAAE;kBAAEkC,SAAS,EAAE,MAAM;kBAAEC,QAAQ,EAAE,OAAO;kBAAE/B,OAAO,EAAC;gBAAc,CAAE;gBAAAX,QAAA,EAEnE1D,SAAS,CAAC,iBAAiB;cAAC;gBAAA4D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eACbtE,OAAA;gBAAOgE,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBACrCjE,OAAA;kBACIoG,IAAI,EAAC,UAAU;kBACfQ,OAAO,EAAEpF,iBAAiB,CAACY,cAAe;kBAC1C+C,QAAQ,EAAGC,CAAC,IAAK;oBACbrC,gBAAgB,CAAC,gBAAgB,EAAEqC,CAAC,CAACC,MAAM,CAACuB,OAAO,CAAC;kBACxD,CAAE;kBACFC,IAAI,EAAC;gBAAgB;kBAAA1C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACFtE,OAAA;kBAAMgE,SAAS,EAAC;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,EACL9C,iBAAiB,CAACY,cAAc,iBAChCpC,OAAA,CAACjB,GAAG;cACHgF,EAAE,EAAC,mBAAmB;cACtBC,SAAS,EAAC,oBAAoB;cAC9BQ,EAAE,EAAE;gBACHwB,OAAO,EAAE,MAAM;gBACfG,UAAU,EAAE,QAAQ;gBACpBF,cAAc,EAAE,eAAe;gBAC/BL,KAAK,EAAC;cACP,CAAE;cAAA3B,QAAA,gBAEFjE,OAAA,CAAChB,UAAU;gBACV+E,EAAE,EAAC,mBAAmB;gBACtBC,SAAS,EAAC,qBAAqB;gBAC/BQ,EAAE,EAAE;kBAAEkC,SAAS,EAAE,MAAM;kBAAEC,QAAQ,EAAE,OAAO;kBAACf,KAAK,EAAC,MAAM;kBAAChB,OAAO,EAAC;gBAAe,CAAE;gBAAAX,QAAA,EAEhF1D,SAAS,CAAC,iCAAiC;cAAC;gBAAA4D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,eACbtE,OAAA;gBAAOgE,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBACtCjE,OAAA;kBACIoG,IAAI,EAAC,UAAU;kBACfQ,OAAO,EAAEpF,iBAAiB,CAACa,4BAA6B;kBACxD8C,QAAQ,EAAGC,CAAC,IAAKrC,gBAAgB,CAAC,8BAA8B,EAAEqC,CAAC,CAACC,MAAM,CAACuB,OAAO,CAAE;kBACpFC,IAAI,EAAC;gBAA8B;kBAAA1C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACFtE,OAAA;kBAAMgE,SAAS,EAAC;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEM,CACL;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,EACL9C,iBAAiB,CAACe,aAAa,KAAK,KAAK,iBACzCvC,OAAA,CAACjB,GAAG;YACHgF,EAAE,EAAC,mBAAmB;YACtBC,SAAS,EAAC,mBAAmB;YAC7BQ,EAAE,EAAE;cAAEiB,aAAa,EAAE,QAAQ;cAAEC,MAAM,EAAE,iBAAiB;cAAEd,OAAO,EAAC;YAAgB,CAAE;YAAAX,QAAA,gBAEpFjE,OAAA,CAAChB,UAAU;cAACgF,SAAS,EAAC,qBAAqB;cAACQ,EAAE,EAAE;gBAAEI,OAAO,EAAE;cAAuB,CAAE;cAAAX,QAAA,EAAE1D,SAAS,CAAC,WAAW;YAAC;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAG1HtE,OAAA,CAACZ,WAAW;cACX8F,OAAO,EAAC,UAAU;cAClBmB,SAAS;cACTrC,SAAS,EAAC,qBAAqB;cAC/BQ,EAAE,EAAE;gBACHqB,YAAY,EAAE,MAAM;gBACpBD,KAAK,EAAE,iBAAiB;gBACxB,0CAA0C,EAAE;kBAAEJ,MAAM,EAAE;gBAAO,CAAC;gBAC9D,gDAAgD,EAAE;kBAAEA,MAAM,EAAE;gBAAO,CAAC;gBACpE,YAAY,EAAE;kBAAEA,MAAM,EAAE;gBAAO,CAAC;gBAChC,qBAAqB,EAAC;kBAACA,MAAM,EAAG;gBAA8B;cAC7D,CAAE;cAAAvB,QAAA,eAEJjE,OAAA,CAACX,MAAM;gBACNwD,KAAK,EAAErB,iBAAiB,CAACc,QAAS,CAAC;gBAAA;gBACnC6C,QAAQ,EAAGC,CAAM,IAAKrC,gBAAgB,CAAC,UAAU,EAAEqC,CAAC,CAACC,MAAM,CAACxC,KAAK,CAAE;gBACnEgE,IAAI,EAAC,UAAU;gBACfrC,EAAE,EAAE;kBAAEoB,KAAK,EAAE,iBAAiB;kBAAEC,YAAY,EAAE;gBAAO,CAAE;gBAAA5B,QAAA,gBAEvDjE,OAAA,CAACV,QAAQ;kBAACuD,KAAK,EAAC,kBAAkB;kBAAAoB,QAAA,EAAE1D,SAAS,CAAC,kBAAkB;gBAAC;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC7EtE,OAAA,CAACV,QAAQ;kBAACuD,KAAK,EAAC,kBAAkB;kBAAAoB,QAAA,EAAE1D,SAAS,CAAC,kBAAkB;gBAAC;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CACL,eACDtE,OAAA,CAACjB,GAAG;YAACiF,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBACjCjE,OAAA,CAAChB,UAAU;cAACgF,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAE1D,SAAS,CAAC,iBAAiB;YAAC;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAIvFtE,OAAA;cAAOgE,SAAS,EAAC,eAAe;cAAAC,QAAA,gBACpCjE,OAAA;gBACIoG,IAAI,EAAC,UAAU;gBACfQ,OAAO,EAAEpF,iBAAiB,CAACe,aAAc;gBACzC4C,QAAQ,EAAGC,CAAC,IAAKrC,gBAAgB,CAAC,eAAe,EAAEqC,CAAC,CAACC,MAAM,CAACuB,OAAO,CAAE;gBACrEC,IAAI,EAAC;cAAe;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,eACFtE,OAAA;gBAAMgE,SAAS,EAAC;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACNtE,OAAA;QAAKgE,SAAS,EAAC,oBAAoB;QAAAC,QAAA,eAClCjE,OAAA,CAACb,MAAM;UACN+F,OAAO,EAAC,WAAW;UACnBhB,OAAO,EAAEJ,kBAAmB;UAC5BE,SAAS,EAAC,WAAW;UAAAC,QAAA,EAEpB1D,SAAS,CAAC,OAAO;QAAC;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAET,CAAC;AAACjE,EAAA,CAteIJ,eAAe;EAAA,QACKH,cAAc,EAiBlCN,cAAc;AAAA;AAAAsH,EAAA,GAlBd7G,eAAe;AAwerB,eAAeA,eAAe;AAAC,IAAA6G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}