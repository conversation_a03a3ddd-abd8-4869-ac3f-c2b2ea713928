import React, { useState, useEffect } from "react";
import "../../guideDesign/Canvas.module.css";
import { Box, Typography, TextField, Grid, IconButton, Tooltip, Button } from "@mui/material";
import ArrowBackIosNewOutlinedIcon from "@mui/icons-material/ArrowBackIosNewOutlined";
import CloseIcon from "@mui/icons-material/Close";
import useDrawerStore from "../../../store/drawerStore";
import { warning } from "../../../assets/icons/icons";
import { useTranslation } from "react-i18next";

const PageInteractions = ({ setShowCanvasSettings, resetHeightofBanner }: any) => {
	const { t: translate } = useTranslation();
	const {
		selectedTemplate,
		padding,
		setPadding,
		position,
		setPosition,
		radius,
		setRadius,
		borderSize,
		setBorderSize,
		setBorderColor,
		borderColor,
		setBackgroundColor,
		backgroundColor,
		backgroundC,
		setBackgroundC,
		Bposition,
		setBposition,
		bpadding,
		setbPadding,
		Bbordercolor,
		setBBorderColor,
		BborderSize,
		setBBorderSize,
		zindeex,
		setZindeex,
		sectionColor,
		setSectionColor,
		setBannerCanvasSetting,
		setOverlayEnabled,
		setIsUnSavedChanges
	} = useDrawerStore((state) => state);

	const handlePositionChange = (pos: any) => {
		setPosition(pos);
	};
	const [tempPadding, setTempPadding] = useState(bpadding);
	const [tempBorderSize, setTempBorderSize] = useState(BborderSize || "0");
	const [tempZIndex, setTempZIndex] = useState(zindeex);
	const [tempBorderColor, setTempBorderColor] = useState(Bbordercolor || "#000000");
	const [tempBackgroundColor, setTempBackgroundColor] = useState(backgroundC);
	const [tempSectionColor, setTempSectionColor] = useState(sectionColor);
	const [tempPosition, setTempPosition] = useState(Bposition || "Cover Top");
	const [isOpen, setIsOpen] = useState(true);
	const [isColorChangesDone, setColorChangesDone] = useState(false);
	const [isPaddingChangeDone, setPaddingChangeDone] = useState(false);
	const [paddingError, setPaddingError] = useState(false);
	const [borderSizeError, setBorderSizeError] = useState(false);

	// Ensure temporary states are initialized with correct defaults
	useEffect(() => {
		setTempBorderSize(BborderSize || "0");
		setTempBorderColor(Bbordercolor || "#000000");
	}, [BborderSize, Bbordercolor]);

	const handleApplyChanges = () => {
		if (paddingError || borderSizeError) {
			return;
		}

		setBposition(tempPosition);
		setbPadding(tempPadding || "10");
		setBBorderSize(tempBorderSize || "0");
		setBBorderColor(tempBorderColor);
		setBackgroundC(tempBackgroundColor);
		setSectionColor(tempSectionColor);
		setZindeex(tempZIndex);

		const paddingToUse = isPaddingChangeDone ? bpadding : tempPadding;
		// Reset height of banner with the correct padding value
		resetHeightofBanner(
			tempPosition,
			parseInt(paddingToUse || "12"),
			parseInt(tempBorderSize || "0"),
			true,
			parseInt(tempPadding || "0")
		);

		// Create canvas settings object with all properties
		const canvasSettings: Record<string, any> = {
			Position: tempPosition,
			BackgroundColor: isColorChangesDone ? tempBackgroundColor : undefined,
			Padding: paddingToUse || "12",
			BorderColor: tempBorderColor,
			BorderSize: tempBorderSize || "0",
			Zindex: tempZIndex || 9999,
			sectionColor: tempSectionColor,
		};

		// Remove undefined properties
		Object.keys(canvasSettings).forEach((key) => {
			if (canvasSettings[key] === undefined) {
				delete canvasSettings[key];
			}
		});

		// Apply canvas settings
		setBannerCanvasSetting(canvasSettings);

		// Close the panel and mark changes as unsaved
		handleClose();
		setIsUnSavedChanges(true);
	};

	const resetTempValues = () => {
		setTempPadding(bpadding);
		setTempBorderSize(BborderSize);
		setTempZIndex(zindeex);
		setTempBorderColor(Bbordercolor);
		setTempBackgroundColor(backgroundC);
		setTempSectionColor(sectionColor);
		setTempPosition(Bposition || "Cover Top");
		setColorChangesDone(false);
		setPaddingChangeDone(false);
		setPaddingError(false);
		setBorderSizeError(false);
	};

	const handleClose = () => {
		resetTempValues();
		setIsOpen(false);
		setShowCanvasSettings(false);
	};

	if (!isOpen) return null;

	const buttonStyle = (isSelected: boolean) => ({
		border: isSelected ? `1px solid var(--primarycolor)` : "none",
		borderRadius: "20px",
		backgroundColor: isSelected ? "#d3d9d9" : "#f1ecec",
		color: "#000",
		cursor: "pointer", //isSelected ? "pointer" : "not-allowed",
		boxShadow: isSelected ? "0 0 2px rgba(0,0,0,0.2)" : "none",
		opacity: isSelected ? "1" : "0.5",
		padding: "5px",
		lineHeight: "15px",
	});

	return (
		<div className="qadpt-designpopup">
			<div className="qadpt-content">
				<div className="qadpt-design-header">
					<IconButton
						aria-label="close"
						onClick={handleClose}
					>
						<ArrowBackIosNewOutlinedIcon />
					</IconButton>
					<div className="qadpt-title">{translate("Canvas", { defaultValue: "Canvas" })}</div>
					<IconButton
						size="small"
						aria-label="close"
						onClick={handleClose}
					>
						<CloseIcon />
					</IconButton>
				</div>

				<div
					style={{
						alignItems: "center",
						backgroundColor: "var(--back-light-color)",
						borderRadius: "var(--button-border-radius)",
						height: "auto",
						margin: "0 15px 5px",
					}}
				>
					<label style={{ fontWeight: "600" }}>{translate("Position", { defaultValue: "Position" })}</label>
					<div style={{ display: "flex", padding: "8px", placeContent: "center", gap: "5px" }}>
						<button
							style={buttonStyle(tempPosition !== "Cover Top")}
							onClick={() => {
								setTempPosition("Push Down");
								// When changing position, mark padding as changed to ensure it's applied
								setPaddingChangeDone(true);
							}}
						>
							{translate("Push Down", { defaultValue: "Push Down" })}
						</button>

						<button
							style={buttonStyle(tempPosition === "Cover Top")}
							onClick={() => {
								setTempPosition("Cover Top");
								// When changing position, mark padding as changed to ensure it's applied
								setPaddingChangeDone(true);
							}}
						>
							{translate("Cover Top", { defaultValue: "Cover Top" })}
						</button>
					</div>
				</div>

				<div className="qadpt-controls">
					<Box className="qadpt-control-box">
						<Typography className="qadpt-control-label">{translate("Padding", { defaultValue: "Padding" })}</Typography>
						<TextField
							variant="outlined"
							value={tempPadding || "0"}
							fullWidth
							size="small"
							className="qadpt-control-input"
							onChange={(e) => {

								const inputValue = parseInt(e.target.value) || 0;

								// Validate padding between 0px and 20px
								if (inputValue < 0 || inputValue > 20) {
									setPaddingError(true);
								} else {
									setPaddingError(false);
								}
								setTempPadding(inputValue.toString());
								setPaddingChangeDone(true);
							}}
							InputProps={{
								endAdornment: "px",
								sx: {
									"&:hover .MuiOutlinedInput-notchedOutline": { border: "none" },
									"&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "none" },
									"& fieldset": { border: "none" },
								},
							}}
							error={paddingError}
						/>
					</Box>
					{paddingError && (
						<Typography
							style={{
								fontSize: "12px",
								color: "#e9a971",
								textAlign: "left",
								top: "100%",
								left: 0,
								marginBottom: "5px",
								display: "flex",
							}}
						><span style={{ display: "flex", fontSize: "12px", alignItems: "center", marginRight: "4px" }}

							dangerouslySetInnerHTML={{ __html: warning }}
							/>
							{translate("padding_range_error", { defaultValue: "Value must be between 0px and 20px." })}
						</Typography>
					)}
					<Box className="qadpt-control-box">
						<Typography className="qadpt-control-label">{translate("Border Size", { defaultValue: "Border Size" })}</Typography>
						<TextField
							variant="outlined"
							value={tempBorderSize || "0"}
							fullWidth
							size="small"
							className="qadpt-control-input"
							onChange={(e) => {

								const inputValue = parseInt(e.target.value) || 0;

								// Validate border size between 0px and 10px
								if (inputValue < 0 || inputValue > 10) {
									setBorderSizeError(true);
								} else {
									setBorderSizeError(false);
								}

								setTempBorderSize(inputValue.toString());
							}}
							InputProps={{
								endAdornment: "px",
								sx: {
									"&:hover .MuiOutlinedInput-notchedOutline": { border: "none" },
									"&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "none" },
									"& fieldset": { border: "none" },
								},
							}}
							error={borderSizeError}
						/>
					</Box>
					{borderSizeError && (
						<Typography
							style={{
								fontSize: "12px",
								color: "#e9a971",
								textAlign: "left",
								top: "100%",
								left: 0,
								marginBottom: "5px",
								display: "flex",
							}}
						><span style={{ display: "flex", fontSize: "12px", alignItems: "center", marginRight: "4px" }}

							dangerouslySetInnerHTML={{ __html: warning }}
							/>
							{translate("border_size_range_error", { defaultValue: "Value must be between 0px and 10px." })}
						</Typography>
					)}
					{/* <Box className="qadpt-control-box">
						<Typography className="qadpt-control-label">Z-Index</Typography>
						<TextField
							variant="outlined"
							value={tempZIndex}
							fullWidth
							margin="dense"
							size="small"
							className="qadpt-control-input"
							onChange={(e) => setTempZIndex(parseInt(e.target.value) || 0)}
						/>
					</Box> */}
					<Box className="qadpt-control-box">
						<Typography className="qadpt-control-label">{translate("Border", { defaultValue: "Border" })}</Typography>
						<input
							type="color"
							value={tempBorderColor}
							onChange={(e) => setTempBorderColor(e.target.value)}
							className="qadpt-color-input"
						/>
					</Box>
					<Box className="qadpt-control-box">
						<Typography className="qadpt-control-label">{translate("Background", { defaultValue: "Background" })}</Typography>
						<input
							type="color"
							value={tempBackgroundColor}
							onChange={(e) => {
								setColorChangesDone(true);
								setTempBackgroundColor(e.target.value);
							}}
							className="qadpt-color-input"
						/>
					</Box>
					{/* <Box className="qadpt-control-box">
						<Typography className="qadpt-control-label">Section Color</Typography>
						<input
							type="color"
							value={tempSectionColor}
							onChange={(e) => setTempSectionColor(e.target.value)}
							className="qadpt-color-input"
						/>
					</Box> */}
				</div>
				<div className="qadpt-drawerFooter">
					<Button
						variant="contained"
						onClick={handleApplyChanges}
						className={`qadpt-btn ${paddingError || borderSizeError ? "disabled" : ""}`}
						disabled={paddingError || borderSizeError}
					>
						{translate("Apply", { defaultValue: "Apply" })}
					</Button>
				</div>
			</div>
		</div>
	);
};

export default PageInteractions;
